import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'

describe('approve()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<AccountContractType>())
  }

  const initData = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('accountが登録されている状態', () => {
      it('allowanceが設定されること', async () => {
        const ownerId = BASE.ACCOUNT.ACCOUNT0.ID
        const spenderId = BASE.ACCOUNT.ACCOUNT1.ID
        const validatorId = BASE.VALID.VALID0.ID
        const amount = 100

        await tokenFuncs.approve({
          token,
          accounts,
          spenderId,
          amount,
          options: {
            // tokenコントラクトを経由してapproveを実行
            validatorId,
            ownerId,
          },
        })

        const result = await tokenFuncs.getAllowance({ token, prams: [validatorId, ownerId, spenderId] })
        assertEqualForEachField(result, { allowance: amount })
      })
    })

    describe('accountにallowanceが設定されている状態', () => {
      it('異なる宛先に対しapproveを行なった場合、異なるallowanceが設定されること', async () => {
        const ownerId = BASE.ACCOUNT.ACCOUNT0.ID
        const spenderId1 = BASE.ACCOUNT.ACCOUNT1.ID
        const spenderId2 = BASE.ACCOUNT.ACCOUNT2.ID
        const validatorId = BASE.VALID.VALID0.ID
        const amount = 150

        await tokenFuncs.approve({
          token,
          accounts,
          spenderId: spenderId2,
          amount,
          options: {
            // tokenコントラクトを経由してapproveを実行
            validatorId,
            ownerId,
          },
        })

        const toAccount1 = await tokenFuncs.getAllowance({ token, prams: [validatorId, ownerId, spenderId1] })
        assertEqualForEachField(toAccount1, { allowance: 100 })
        const toAccount2 = await tokenFuncs.getAllowance({ token, prams: [validatorId, ownerId, spenderId2] })
        assertEqualForEachField(toAccount2, { allowance: amount })
      })

      it('金額を変更しapproveを行なった場合、allowanceが更新されること', async () => {
        const ownerId = BASE.ACCOUNT.ACCOUNT0.ID
        const spenderId = BASE.ACCOUNT.ACCOUNT1.ID
        const validatorId = BASE.VALID.VALID0.ID
        const amount = 200

        await tokenFuncs.approve({
          token,
          accounts,
          spenderId,
          amount,
          options: {
            // tokenコントラクトを経由してapproveを実行
            validatorId,
            ownerId,
          },
        })

        const result = await tokenFuncs.getAllowance({ token, prams: [validatorId, ownerId, spenderId] })
        assertEqualForEachField(result, { allowance: amount })
      })
    })
  })
})
