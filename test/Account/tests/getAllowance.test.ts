import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import {
  AccountInstance,
  FinancialCheckInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'
import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'

// Chai global vars
declare let assert: Chai.Assert

describe('getAllowance()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let financialCheck: FinancialCheckInstance
  let accounts: SignerWithAddress[]
  let fakeFinancialCheck
  // getAllowance のテストは validator.getAllowance から呼ばれるため validator のテストで実施する, ここでは呼び出し元検証のみ行う

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account, token, financialCheck } =
      await contractFixture<AccountContractType>())
  }

  const initData = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
    for (const accountId of [
      BASE.ACCOUNT.ACCOUNT0.ID,
      BASE.ACCOUNT.ACCOUNT1.ID,
      BASE.ACCOUNT.ACCOUNT2.ID,
      BASE.ACCOUNT.ACCOUNT3.ID,
      BASE.ACCOUNT.ACCOUNT4.ID,
    ]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
    await tokenFuncs.approve({
      token,
      accounts,
      spenderId: BASE.ACCOUNT.ACCOUNT1.ID,
      amount: 200,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await helpers.impersonateAccount(await financialCheck.getAddress())
    await helpers.setBalance(await financialCheck.getAddress(), 100n ** 18n)
    fakeFinancialCheck = await ethers.getSigner(await financialCheck.getAddress())
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
        const result = account.connect(accounts[1]).getAllowance(BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID)
        await expect(result).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })

    describe('getAllowance fake call from token and financialCheck', () => {
      before(async () => {
        await initData()
      })

      it('should return same allowance data as normal when call from token', async () => {
        // Hack: Fake financialCheck call to getAllowance
        // UNREACHABLE false case: This test only for coverage, can be remove when financialCheck is implemented
        const result = await account
          .connect(fakeFinancialCheck)
          .getAllowance(BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID)

        assertEqualForEachField(result, { allowance: 200n })
      })

      it('should return zero data and error when owner not exist', async () => {
        // Hack: Fake financialCheck and token can call getAllowance
        // Because we pass above case, only test from fake token
        // UNREACHABLE false case: This test only for coverage, not for real case
        const result = await account
          .connect(fakeFinancialCheck)
          .getAllowance(BASE.ACCOUNT.ACCOUNT9.ID, BASE.ACCOUNT.ACCOUNT1.ID)
        assert.equal(result.err, ERR.ACCOUNT.OWNER_NOT_EXIST, 'err')
      })
    })
  })
})
