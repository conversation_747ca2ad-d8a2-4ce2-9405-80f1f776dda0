import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance, IssuerInstance } from '@test/common/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'
import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'

describe('setAccountStatus()', () => {
  let issuer: IssuerInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]
  // setAccountStatus のテストは issuer.setAccountStatus から呼ばれるため issuer のテストで実施する, ここでは呼び出し元検証のみ行う

  const setupFixture = async () => {
    ;({ accounts, issuer, account } = await contractFixture<AccountContractType>())
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がIssuerではない場合、エラーがスローされること', async () => {
        await expect(
          account
            .connect(accounts[1])
            .setAccountStatus(BASE.ACCOUNT.ACCOUNT0.ID, BASE.STATUS.ACTIVE, BASE.REASON_CODE1, BASE.TRACE_ID), // issuerコントラクト以外をfromに設定する
        ).to.be.revertedWith(ERR.ISSUER.NOT_ISSUER_CONTRACT)
      })
    })

    describe('fake call from issuer', () => {
      it('should revert when accountId is not registered and call from fake issuer', async () => {
        // Hack: Fake issuer can call setAccountStatus
        // UNREACHABLE false case: This test only for coverage, not for real case
        await helpers.impersonateAccount(await issuer.getAddress())
        await helpers.setBalance(await issuer.getAddress(), 100n ** 18n)
        const fakeIssuer = await ethers.getSigner(await issuer.getAddress())
        await expect(
          account
            .connect(fakeIssuer)
            .setAccountStatus(BASE.ACCOUNT.ACCOUNT1.ID, BASE.STATUS.ACTIVE, BASE.REASON_CODE1, BASE.TRACE_ID), // Fake issuer call
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
