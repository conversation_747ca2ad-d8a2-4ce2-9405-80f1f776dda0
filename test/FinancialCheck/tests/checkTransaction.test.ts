import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialCheckInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getDeadline, siginfoGenerator, toBytes32, toBytes } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ContractTransactionResponse } from 'ethers'
import { before } from 'mocha'
import privateKey from '@/privateKey'

const valid0 = { eoaKey: 1, ...BASE.VALID.VALID0 }
const valid1 = { eoaKey: 2, ...BASE.VALID.VALID1 }

let provider: ProviderInstance
let issuer: IssuerInstance
let validator: ValidatorInstance
let token: TokenInstance
let financialCheck: FinancialCheckInstance
let contractManager: ContractManagerInstance
let ibcToken: IBCTokenInstance
let businessZoneAccount: BusinessZoneAccountInstance
let accounts: SignerWithAddress[]

const setupFixture = async () => {
  ;({ accounts, provider, issuer, validator, token, financialCheck, contractManager, ibcToken, businessZoneAccount } =
    await contractFixture<FinancialCheckContractType>())
}

describe('checkTransaction()', () => {
  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 3000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await tokenFuncs.approve({
          token,
          accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT3.ID,
          amount: 100,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('移転可能かどうか確認できること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('sendAccountIdとfromAccountIdが異なる場合に移転可能かどうか確認できること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('sendAccountIdとfromAccountIdが異なり、accountSignatureとsigInfoが空であった場合にアカウント署名がスキップされ処理が成功すること', async () => {
        const siginfo = {
          info: '0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
          signer:
            '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
        }
        const accountSignature =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            accountSignature: accountSignature,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('Bizアカウントが登録されている状態', () => {
      let ibcAddress
      let ibcAddressString
      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        const amount = 100

        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })

        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: amount,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })
      })

      it('Bizからのリクエストで移転可能かどうか確認できること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 10,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('BizZoneからのリクエストでaccountSignatureとsigInfoが空であった場合に、アカウント署名がスキップされ処理が成功すること', async () => {
        const siginfo = {
          info: '0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
          signer:
            '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
        }
        const accountSignature =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 10,
          sigInfo: siginfo,
          options: {
            accountSignature: accountSignature,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 5)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        // transferLimit    = 1000
        // chargeLimit      = 2000
        // mintLimit        = 3000
        // burnLimit        = 4000
        // cumulativeLimit  = 5000
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        // cumulativeLimit  = 5000 - 1000 = 4000
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          },
        })
      })

      it('fromAccountと同じtoAccountを指定した場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.VALID.FROM_TO_SAME,
        }
        assertEqualForEachField(result, expected)
      })

      it('fromAccountが存在しない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT11.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('fromAccountのステータスが"active"以外である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })

      it('sendAccountが存在しない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT11.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.SEND_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('sendAccountのステータスが"active"以外である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.SEND_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })

      it('toAccountが存在しない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT11.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.TO_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('toAccountのステータスが"active"以外である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.TO_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })

      it('日次の累積限度額を超えている場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        // 限度額調整のため追加でmintを4回同時実行
        const mintPromises: Promise<ContractTransactionResponse>[] = []
        for (let i = 0; i < 4; i++) {
          mintPromises.push(
            tokenFuncs.mint({
              token,
              accounts,
              amount: 1000,
              options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
            }),
          )
        }
        await Promise.all(mintPromises)
        // cumulativeLimit  = 4000 - 4000 = 0
        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 800,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT,
        }
        assertEqualForEachField(result, expected)
      })

      it('移転限度額を超えている場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        // 限度額調整のため累積限度額リセット
        await issuerFuncs.cumulativeReset({
          issuer,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 1500,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.FINACCOUNT.EXCEEDED_TRANSFER_LIMIT,
        }
        assertEqualForEachField(result, expected)
      })

      it('日次の移転累積限度額を超えている場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        // 移転限度額調整のため追加でtransferを4回同時実行
        const transferPromises: Promise<ContractTransactionResponse>[] = []
        for (let i = 0; i < 8; i++) {
          transferPromises.push(
            tokenFuncs.transferSingle({
              token,
              accounts,
              amount: 100,
              miscValue1: toBytes32('0'),
              miscValue2: toBytes32('0'),
              options: {
                sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
                fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
                toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
              },
            }),
          )
        }
        await Promise.all(transferPromises)
        // cumulativeTransferLimit  = 800 - 800 = 0
        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.FINACCOUNT.EXCEEDED_DAILY_TRANSFER_LIMIT,
        }
        assertEqualForEachField(result, expected)
      })

      it('日付が変わった場合、移転累積限度額を超えない金額では正常に処理されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        // increase time by 24 hours
        await time.increase(24 * 60 * 60)
        //cumulativeTransferLimit = 800
        const amount = 100
        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: amount,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('日付が変わった場合、移転累積限度額を超える金額ではエラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        // increase time by 24 hours
        await time.increase(24 * 60 * 60)
        //cumulativeTransferLimit = 800
        const amount = 900
        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: amount,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.FINACCOUNT.EXCEEDED_DAILY_TRANSFER_LIMIT,
        }
        assertEqualForEachField(result, expected)
      })

      // it('バリデータ署名が不正である場合、エラーが返却されること', async () => {
      //   const pt = await getDeadline();
      //   const siginfo = await siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

      //   const result = await financialCheckFuncs.checkTransaction(
      //     financialCheck,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ZONE_ID.ID0,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ACCOUNT.ACCOUNT2.ID,
      //     100,
      //     siginfo,
      //     {
      //       privateKeyForSig: privateKey.key[valid0.eoaKey],
      //       eoaKey: valid1.eoaKey,
      //     },
      //   );
      //   const expected = {
      //     success: false,
      //     err: ERR.VALID.VALIDATOR_NOT_ROLE,
      //   };
      //   assertEqualForEachField(result, expected);
      // });

      // it('バリデータ署名が有効期限切れである場合、エラーが返却されること', async () => {
      //   const exceededDeadline = await getExceededDeadline();
      //   const pt = await getDeadline();
      //   const siginfo = await siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

      //   const result = await financialCheckFuncs.checkTransaction(
      //     financialCheck,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ZONE_ID.ID0,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ACCOUNT.ACCOUNT2.ID,
      //     100,
      //     siginfo,
      //     {
      //       privateKeyForSig: privateKey.key[valid0.eoaKey],
      //       eoaKey: valid0.eoaKey,
      //       deadline: exceededDeadline,
      //     },
      //   );
      //   const expected = {
      //     success: false,
      //     err: ERR.ACTRL.ACTRL_SIG_TIMEOUT,
      //   };
      //   assertEqualForEachField(result, expected);
      // });

      it('アカウント署名が無効である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        const invalidAccountSig = privateKey.sig(
          siginfo.signer,
          ['bytes32', 'bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
          [
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
            10,
            BASE.ACCOUNT_SIG_MSG.TRANSFER,
          ],
        )[0]

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
            accountSignature: invalidAccountSig,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
        }
        assertEqualForEachField(result, expected)
      })

      it('空のアカウント署名が無効である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        const emptyAccountSig = toBytes('')

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
            accountSignature: emptyAccountSig,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('Bizアカウントが登録されている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        const amount = 100

        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountStatus: BASE.STATUS.TERMINATING,
          },
        })

        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: amount,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })
      })

      it('fromAccountがBiz側に存在しない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT4.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('fromAccountのBiz側のステータスが"active"以外である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })

      it('sendAccountがBiz側に存在しない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.SEND_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('sendAccountのBiz側のステータスが"active"以外である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.SEND_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })

      it('toAccountがBiz側に存在しない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT4.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.TO_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('toAccountのBiz側のステータスが"active"以外である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.TO_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })
    })
    describe('account balance and status check', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(5, 10)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await Promise.all(
          params.map(async (v) => {
            await validatorFuncs.addAccount({
              validator,
              accounts,
              options: {
                validatorId: BASE.VALID.VALID0.ID,
                accountId: v.accountId,
                accountName: v.accountName,
              },
            })
            await issuerFuncs.addAccountRole({
              issuer,
              accounts,
              options: {
                issuerId: BASE.ISSUER.ISSUER0.ID,
                accountId: v.accountId,
                deadline: deadline + 60,
              },
            })
          }),
        )
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 3000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await tokenFuncs.approve({
          token,
          accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT3.ID,
          amount: 100,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await tokenFuncs.approve({
          token,
          accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT5.ID,
          amount: 500,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          },
        })
      })

      it('miscValue2が4096よりも大きい場合、エラーが返却されること', async () => {
        await time.increase(24 * 60 * 60)
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 1,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
          tokenOptions: {
            miscValue2: BASE.MISC_VALUE2_4097,
          },
        })
        const expected = {
          success: false,
          err: ERR.TOKEN.TOKEN_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when balance is not enough in Financial zone', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 10000,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.BALANCE_NOT_ENOUGH,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when balance is not enough in other zone', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 10000,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.BALANCE_NOT_ENOUGH,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when fromAccount is disable', async () => {
        // Disable account on BusinessZoneAccount
        await validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_STATUS_IS_DISABLED,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when fromAccount is not exist', async () => {
        // Disable account on BusinessZoneAccount
        await validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID1,
          sendAccountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT11.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_IS_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when sendAccount is invalid value', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.EMPTY.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.SEND_ACCOUNT_IS_INVALID_VALUE,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when fromAccount is invalid value', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountId: BASE.ACCOUNT.EMPTY.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_ACCOUNT_IS_INVALID_VALUE,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and error message when toAccount is invalid value', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          toAccountId: BASE.ACCOUNT.EMPTY.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.TO_ACCOUNT_IS_INVALID_VALUE,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('from and to account issuers are different', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 5)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        // add new issuer
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            name: BASE.ISSUER.ISSUER1.NAME,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        await issuerFuncs.addIssuerRole({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
        await validatorFuncs.addValidator({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            name: BASE.VALID.VALID1.NAME,
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
          },
        })

        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            accountName: BASE.ACCOUNT.ACCOUNT5.NAME,
          },
        })
        await issuerFuncs.addAccountRole({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            deadline: deadline + 60,
          },
        })
      })

      it('should return false and error message when from and to account issuers are different', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkTransaction({
          financialCheck,
          zoneId: BASE.ZONE_ID.ID0,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromAccountIssuerId: BASE.ISSUER.ISSUER0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
