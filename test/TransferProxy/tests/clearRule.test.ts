import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { TransferProxyInstance } from '@test/common/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('clearRule', () => {
  let accounts: SignerWithAddress[]
  let transferProxy: TransferProxyInstance

  const setupFixture = async () => {
    ;({ accounts, transferProxy } = await contractFixture<TransferProxyContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('未登録の状態でClearする', async function () {
        await transferProxyFuncs.clearRule({ transferProxy: transferProxy })
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 0, 'Rule')
      })
    })

    describe('Ruleが5件登録されている状態', () => {
      before(async () => {
        for (let i = 0; i < 5; i++) {
          const rule = await accounts[i].getAddress()
          const position = i
          await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        }
      })

      it('全てのRuleをClearする', async function () {
        await transferProxyFuncs.clearRule({ transferProxy: transferProxy })
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 0, 'Rule')
      })
    })
  })
})
