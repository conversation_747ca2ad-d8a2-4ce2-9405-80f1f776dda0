import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert
const NOT_REG_ISSUER_ID = toBytes32('x399')

describe('getIssuer()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
  }

  const setupMultipleIssuers = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
        name: BASE.ISSUER.EMPTY.NAME,
      },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await setupMultipleIssuers()
      })

      it('issuer情報が取得できること /* DCPF-21196', async () => {
        const result = await issuerFuncs.getIssuer({ issuer, params: [BASE.ISSUER.ISSUER0.ID] })

        assertEqualForEachField(result, {
          bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          name: BASE.ISSUER.ISSUER0.NAME,
          err: '',
        })
      })

      it('空issuerNameが取得できること', async () => {
        const result = await issuerFuncs.getIssuer({ issuer, params: [BASE.ISSUER.ISSUER1.ID] })

        assertEqualForEachField(result, {
          bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          name: BASE.ISSUER.EMPTY.NAME,
          err: '',
        })
      })

      it('未登録issuerId指定する場合、エラーが返されること', async () => {
        const result = await issuerFuncs.getIssuer({ issuer, params: [NOT_REG_ISSUER_ID] })

        assert.equal(result.err, ERR.ISSUER.ISSUER_ID_NOT_EXIST, 'err')
      })
    })
  })
})
