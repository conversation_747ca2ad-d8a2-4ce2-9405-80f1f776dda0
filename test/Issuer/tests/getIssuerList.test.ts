import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'
import '@nomicfoundation/hardhat-chai-matchers'

describe('getIssuerList()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
  }

  const setupMultipleIssuers = async () => {
    const addIssuerParams = [
      BASE.ISSUER.ISSUER0,
      BASE.ISSUER.ISSUER1,
      BASE.ISSUER.ISSUER2,
      BASE.ISSUER.ISSUER3,
      BASE.ISSUER.ISSUER4,
      BASE.ISSUER.ISSUER5,
      BASE.ISSUER.ISSUER6,
      BASE.ISSUER.ISSUER7,
      BASE.ISSUER.ISSUER8,
      BASE.ISSUER.ISSUER9,
      BASE.ISSUER.ISSUER10,
      BASE.ISSUER.ISSUER11,
      BASE.ISSUER.ISSUER12,
      BASE.ISSUER.ISSUER13,
      BASE.ISSUER.ISSUER14,
      BASE.ISSUER.ISSUER15,
      BASE.ISSUER.ISSUER16,
      BASE.ISSUER.ISSUER17,
      BASE.ISSUER.ISSUER18,
      BASE.ISSUER.ISSUER19,
    ]

    const deadline = await utils.getDeadline()
    // 20件分のaddIssuerを連続実行することで "sig timeout" が発生するためdeadlineにさらに30秒加算している
    for (let i = 0; i < addIssuerParams.length; i++) {
      await issuerFuncs.addIssuer({
        issuer,
        accounts,
        options: {
          deadline: deadline + 30,
          issuerId: addIssuerParams[i].ID,
          bankCode: addIssuerParams[i].BANK_CODE,
          name: addIssuerParams[i].NAME,
        },
      })
    }
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerが登録されている状態', () => {
      const addIssuerParams = [
        BASE.ISSUER.ISSUER0,
        BASE.ISSUER.ISSUER1,
        BASE.ISSUER.ISSUER2,
        BASE.ISSUER.ISSUER3,
        BASE.ISSUER.ISSUER4,
        BASE.ISSUER.ISSUER5,
        BASE.ISSUER.ISSUER6,
        BASE.ISSUER.ISSUER7,
        BASE.ISSUER.ISSUER8,
        BASE.ISSUER.ISSUER9,
        BASE.ISSUER.ISSUER10,
        BASE.ISSUER.ISSUER11,
        BASE.ISSUER.ISSUER12,
        BASE.ISSUER.ISSUER13,
        BASE.ISSUER.ISSUER14,
        BASE.ISSUER.ISSUER15,
        BASE.ISSUER.ISSUER16,
        BASE.ISSUER.ISSUER17,
        BASE.ISSUER.ISSUER18,
        BASE.ISSUER.ISSUER19,
      ]

      const assertList = (
        result: PromiseType<ReturnType<typeof issuerFuncs.getIssuerList>>,
        expected: typeof addIssuerParams,
      ) => {
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.issuers[i], {
            issuerId: v.ID,
            bankCode: v.BANK_CODE,
            name: v.NAME,
          })
        })
      }

      before(async () => {
        await setupMultipleIssuers()
      })

      it('offset0, limit3を指定した場合、1要素目から3件取得できること', async () => {
        // 先頭からlimit件数分を取得
        const offset = 0
        const limit = 3

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(result, addIssuerParams.slice(1, 4))
      })

      it('offset2, limit2を指定した場合、3要素目から2件取得できること', async () => {
        // ここでは取得データのID以外の値も確認
        const offset = 2
        const limit = 2

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(result, addIssuerParams.slice(3, 4))
      })

      it('offset0, limit10を指定した場合、1要素目から10件取得できること', async () => {
        // ここでは取得データのID以外の値も確認
        const offset = 0
        const limit = 10

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(result, addIssuerParams.slice(1, 11))
      })

      it('offset1, limit10を指定した場合、2要素目から10件取得できること', async () => {
        // ここでは取得データのID以外の値も確認
        const offset = 1
        const limit = 10

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(result, addIssuerParams.slice(2, 12))
      })

      it('最後の1件が取得できること', async () => {
        // 最後の１件のみ取得
        const offset = 18
        const limit = 1

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(result, [addIssuerParams[addIssuerParams.length - 1]])
      })

      it('limitが取得上限(100件)以下の場合、issuerリストが取得できること (limit 100)', async () => {
        // limitが取得上限(100件)以下の場合は検索できる
        const offset = 0
        const limit = 100

        const expectIssuerParams = [
          BASE.ISSUER.ISSUER1,
          BASE.ISSUER.ISSUER2,
          BASE.ISSUER.ISSUER3,
          BASE.ISSUER.ISSUER4,
          BASE.ISSUER.ISSUER5,
          BASE.ISSUER.ISSUER6,
          BASE.ISSUER.ISSUER7,
          BASE.ISSUER.ISSUER8,
          BASE.ISSUER.ISSUER9,
          BASE.ISSUER.ISSUER10,
          BASE.ISSUER.ISSUER11,
          BASE.ISSUER.ISSUER12,
          BASE.ISSUER.ISSUER13,
          BASE.ISSUER.ISSUER14,
          BASE.ISSUER.ISSUER15,
          BASE.ISSUER.ISSUER16,
          BASE.ISSUER.ISSUER17,
          BASE.ISSUER.ISSUER18,
          BASE.ISSUER.ISSUER19,
        ]

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(result, expectIssuerParams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })

      it('limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること', async () => {
        const offset = 0
        const limit = 50

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        assertList(result, addIssuerParams.slice(1, 20))
      })

      it('複数のgetIssuerList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと /* DCPF-21196', async () => {
        // 同時に異なるoffsetとlimitでリクエストを発行
        const requests = [
          issuerFuncs.getIssuerList({ issuer, params: [3, 0] }), // 最初の3件
          issuerFuncs.getIssuerList({ issuer, params: [4, 2] }), // 3番目から次の4件
          issuerFuncs.getIssuerList({ issuer, params: [5, 5] }), // 6番目から次の5件
        ]

        const results = await Promise.all(requests)

        // 各リクエストの結果を検証
        utils.assertEqualForEachField(results[0], { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(results[0], addIssuerParams.slice(1, 4))

        utils.assertEqualForEachField(results[1], { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(results[1], addIssuerParams.slice(3, 7))

        utils.assertEqualForEachField(results[2], { totalCount: addIssuerParams.length - 1, err: '' })
        assertList(results[2], addIssuerParams.slice(6, 11))
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        // limitが取得上限(100件)より大きい場合はエラーになる
        const offset = 0
        const limit = 101

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ISSUER.ISSUER_TOO_LARGE_LIMIT })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること /* DCPF-21196', async () => {
        const offset = addIssuerParams.length + 1 // 配列の長さを超える
        const limit = 20

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ISSUER.ISSUER_OFFSET_OUT_OF_INDEX })
      })

      it('offsetが配列の長さと同じ場合、エラーが返されること /* DCPF-21196', async () => {
        const offset = addIssuerParams.length // 配列の長さと同じ
        const limit = 5

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ISSUER.ISSUER_OFFSET_OUT_OF_INDEX })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerが登録されていない状態', () => {
      const addIssuerParams = [BASE.ISSUER.EMPTY]
      const assertList = (
        result: PromiseType<ReturnType<typeof issuerFuncs.getIssuerList>>,
        expected: typeof addIssuerParams,
      ) => {
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.issuers[i], {
            issuerId: v.ID,
            name: v.NAME,
          })
        })
      }

      it('issuerが登録されていない場合、空リストが取得できること /* DCPF-21196', async () => {
        const offset = 0
        const limit = 100

        const result = await issuerFuncs.getIssuerList({ issuer, params: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })
    })
  })
})
