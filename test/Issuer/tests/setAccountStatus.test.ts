import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { expect } from 'chai'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = toBytes32('x399')

describe('setAccountStatus()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
      })

      it('Accountを凍結できること', async () => {
        await issuerFuncs.setAccountStatus({ issuer, accounts, accountStatus: BASE.STATUS.FROZEN })

        const result = await issuerFuncs.getAccount({
          issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        // アカウントステータスが"frozen"となっていることを確認
        assertEqualForEachField(result, {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          accountStatus: BASE.STATUS.FROZEN,
          err: '',
        })
      })
      it('Accountを凍結解除できること', async () => {
        await issuerFuncs.setAccountStatus({ issuer, accounts, accountStatus: BASE.STATUS.ACTIVE })

        const result = await issuerFuncs.getAccount({
          issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        // アカウントステータスが"frozen"となっていることを確認
        assertEqualForEachField(result, {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          accountStatus: BASE.STATUS.ACTIVE,
          err: '',
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
      })

      it('should revert when issuerId is not valid', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: { issuerId: BASE.ISSUER.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('Accountが存在しない場合、凍結に失敗すること', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT11.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('Accountを強制償却済みからアクティブにできること', async () => {
        const result = issuerFuncs.setAccountStatus({ issuer, accounts, accountStatus: BASE.STATUS.FORCE_BURNED })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })

      it('Accountが凍結以外の状態の場合、凍結解除に失敗すること', async () => {
        const result = issuerFuncs.setAccountStatus({ issuer, accounts, accountStatus: BASE.STATUS.ACTIVE })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED)
      })

      it('Accountがアクティブ以外の状態の場合、凍結に失敗すること', async () => {
        await issuerFuncs.setAccountStatus({ issuer, accounts, accountStatus: BASE.STATUS.FROZEN })
        const result = issuerFuncs.setAccountStatus({ issuer, accounts, accountStatus: BASE.STATUS.FROZEN })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_DISABLED)
      })

      it('未登録issuerIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.cumulativeReset({ issuer, accounts, options: { issuerId: NOT_REG_ISSUER_ID } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.ACTIVE,
          options: { accountId: BASE.ACCOUNT.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('Issuer署名が不正である場合、凍結に失敗すること', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('Issuer署名が不正である場合、凍結解除に失敗すること', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.ACTIVE,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.ACTIVE,
          options: { eoaKey: BASE.EOA.ADMIN },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })
  })
})
