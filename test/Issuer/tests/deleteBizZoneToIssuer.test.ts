import assert from 'assert'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddBizZoneToIssuerOption, FinancialCheckInstance, IssuerInstance, ProviderInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('deleteBizZoneToIssuer()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let financialCheck: FinancialCheckInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ provider, accounts, issuer, financialCheck } = await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
  }

  const setupAdditionalIssuerAndZone = async () => {
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        name: BASE.ISSUER.ISSUER1.NAME,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        zoneName: BASE.ZONE_NAME.NAME2,
      },
    })
  }

  const setupBizZoneMapping = async () => {
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER0.ID,
        zoneId: BASE.ZONE_ID.ID2,
      },
    })
  }

  const setupBasicRolesWithZone = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME1,
      },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, issuer が登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAdditionalIssuerAndZone()
        await setupBizZoneMapping()
      })
      it('issuerが削除されること', async () => {
        const beforeResult = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID2, 0, 100],
        })
        const expected = {
          issuers: [
            {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              name: BASE.ISSUER.ISSUER0.NAME,
              bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
            },
          ],
          totalCount: 1,
          err: '',
        }
        beforeResult.issuers.forEach((v, i) => {
          utils.assertEqualForEachField(v, {
            issuerId: expected.issuers[i].issuerId,
            name: expected.issuers[i].name,
            bankCode: String(expected.issuers[i].bankCode),
          })
        })
        await issuerFuncs.deleteBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        const result = await providerFuncs.getAvailableIssuerIds({ provider, options: [BASE.ZONE_ID.ID0] })
        assert.equal(result.length, 0)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = issuerFuncs.deleteBizZoneToIssuer({
          issuer,
          accounts,
          options: { eoaKey: BASE.EOA.ISSUER2 },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const result = issuerFuncs.deleteBizZoneToIssuer({
          issuer,
          accounts,
          options: { deadline: exceededDeadline },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = issuerFuncs.deleteBizZoneToIssuer({ issuer, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
    })

    describe('IssuerRoleが登録されている状態', () => {
      before(async () => {
        await setupBasicRolesWithZone()
      })

      it('存在しないゾーンを指定した場合、エラーがスローされること', async () => {
        const params: AddBizZoneToIssuerOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          zoneId: BASE.ZONE_ID.ID2,
        }

        const result = issuerFuncs.deleteBizZoneToIssuer({ issuer, accounts, options: params })
        await expect(result).to.be.revertedWith(ERR.PROV.ZONE_NOT_EXIST)
      })
    })
  })
})
