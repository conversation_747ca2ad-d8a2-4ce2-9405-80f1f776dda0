import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { before } from 'mocha'

const TERMINATED_ACCOUNT_ID = BASE.ACCOUNT.ACCOUNT6.ID

describe('checkMint()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer, validator, provider, token, financialZoneAccount } =
      await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  const setupTokenMint = async () => {
    await tokenFuncs.mint({ token, accounts, amount: 100 })
  }

  const setupAdditionalIssuer = async () => {
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER2.ID,
        bankCode: BASE.ISSUER.ISSUER2.BANK_CODE,
      },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuer, provider, providerRole, tokenが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupTokenMint()
      })

      it('mintRoleがある場合、trueが取得できること', async () => {
        const result = await issuerFuncs.checkMint({ issuer, amount: 100 })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuer, provider, providerRole, tokenが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupTokenMint()
      })

      it('should return error when exceeded mint limit', async () => {
        const limitValues = {
          mint: 300,
          burn: 1400,
          charge: 1200,
          discharge: 100,
          transfer: 1100,
          cumulative: {
            total: 100,
            mint: 190,
            burn: 10,
            charge: 10,
            discharge: 10,
            transfer: 10,
          },
        }

        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitValues, BASE.TRACE_ID],
        })

        const result = await issuerFuncs.checkMint({ issuer, amount: 301 })

        utils.assertEqualForEachField(result, { success: false, err: ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT })
      })

      it('should return error when exceed daily limit', async () => {
        const result = await issuerFuncs.checkMint({ issuer, amount: 85 })

        utils.assertEqualForEachField(result, { success: false, err: ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT })
      })

      it('should return error when exceed daily mint limit', async () => {
        const result = await issuerFuncs.checkMint({ issuer, amount: 300 })

        utils.assertEqualForEachField(result, { success: false, err: ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT })
      })

      it('The system should return an error if a minting action exceeds the accumulated limit after the date changes', async () => {
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const result = await issuerFuncs.checkMint({ issuer, amount: 200 })

        utils.assertEqualForEachField(result, { success: false, err: ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT })
      })

      it('If the date changes, the minting will be processed normally as long as the cumulative minting limit is not exceeded', async () => {
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const result = await issuerFuncs.checkMint({ issuer, amount: 50 })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('should return error when account is terminated', async () => {
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
        await validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })

        const result = await issuerFuncs.checkMint({
          issuer,
          amount: 100,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_DISABLED })
      })
    })

    describe('issuerRoleが登録されていない状態', () => {
      before(async () => {
        await setupAdditionalIssuer()
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        const result = await issuerFuncs.checkMint({
          issuer,
          amount: 100,
          options: {
            issuerId: BASE.ISSUER.ISSUER2.ID,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ISSUER.ISSUER_NOT_ROLE })
      })
    })

    describe('issuerRoleが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        for (const _issuer of [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1]) {
          await issuerFuncs.addIssuerRole({ issuer, accounts, options: { issuerId: _issuer.ID } })
        }
        await validatorFuncs.addValidator({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            validatorId: BASE.VALID.VALID1.ID,
          },
        })
      })

      it('issuerに紐付けられていないaccountIdを指定した場合、エラーがスローされること', async () => {
        const result = await issuerFuncs.checkMint({
          issuer,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT7.ID },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('解約されたaccountIdを指定した場合、エラーがスローされること', async () => {
        const result = await issuerFuncs.checkMint({
          issuer,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT6.ID },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_DISABLED })
      })

      it('署名が不正である場合、エラーがスローされること', async () => {
        const result = await issuerFuncs.checkMint({ issuer, amount: 100, options: { sig: ['0x1234', ''] } })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        const result = await issuerFuncs.checkMint({
          issuer,
          amount: 100,
          options: { deadline: exceededDeadline },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
