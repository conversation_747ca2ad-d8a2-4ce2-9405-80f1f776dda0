import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddIssuerOption, IssuerInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addIssuer()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('issuerが登録されること', async () => {
        const params: AddIssuerOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          name: BASE.ISSUER.ISSUER0.NAME,
        }

        const tx = await issuerFuncs.addIssuer({ issuer, accounts, options: params })

        await expect(tx)
          .to.emit(issuer, 'AddIssuer')
          .withArgs(...Object.values(params), anyValue)

        const result = await issuerFuncs.getIssuer({ issuer, params: [params.issuerId] })

        assertEqualForEachField(result, { bankCode: params.bankCode, name: params.name, err: '' })
      })

      it('issuerNameが空文字指定の場合でも、issuerが登録されること', async () => {
        const params: AddIssuerOption = {
          issuerId: BASE.ISSUER.ISSUER1.ID,
          bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          name: BASE.ISSUER.EMPTY.NAME,
        }

        const tx = await issuerFuncs.addIssuer({ issuer, accounts, options: params })

        await expect(tx)
          .to.emit(issuer, 'AddIssuer')
          .withArgs(...Object.values(params), anyValue)

        const result = await issuerFuncs.getIssuer({ issuer, params: [params.issuerId] })
        assertEqualForEachField(result, { bankCode: params.bankCode, name: params.name, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('Deccuret以外のIssuerが最初に追加されようとした場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: { bankCode: BASE.ISSUER.ISSUER10.BANK_CODE },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_DECURRET)
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addIssuer({ issuer, accounts, options: { eoaKey: BASE.EOA.ISSUER1 } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await getExceededDeadline()
        const result = issuerFuncs.addIssuer({ issuer, accounts, options: { deadline: exceededDeadline } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addIssuer({ issuer, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addIssuer({ issuer, accounts, options: { issuerId: BASE.ISSUER.EMPTY.ID } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
      })

      it('既存issuerIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: { bankCode: BASE.ISSUER.ISSUER1.BANK_CODE },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_EXIST)
      })

      it('既存bankCodeを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER2.ID,
            bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_EXIST_BANK_CODE)
      })
    })
  })
})
