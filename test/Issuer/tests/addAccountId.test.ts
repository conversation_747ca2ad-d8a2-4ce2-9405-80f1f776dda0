import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = toBytes32('x399')

// addAccountIdのテストはvalidator.addAccountから呼ばれるためvalidatorのテストで実施する
describe('addAccountId()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupMultipleAccounts = async () => {
    for (const v of [
      BASE.ACCOUNT.ACCOUNT1,
      BASE.ACCOUNT.ACCOUNT2,
      BASE.ACCOUNT.ACCOUNT3,
      BASE.ACCOUNT.ACCOUNT4,
      BASE.ACCOUNT.ACCOUNT5,
    ]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId: v.ID } })
    }
  }

  const setupFakeValidator = async () => {
    await helpers.impersonateAccount(await validator.getAddress()) // impersonate validator
    await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
    return await ethers.getSigner(await validator.getAddress())
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('should revert when called directly', async () => {
        await expect(
          issuer.addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })

    describe('issuerId not valid or exist', () => {
      let fakeValidator
      before(async () => {
        await setupBasicRoles()
        await setupMultipleAccounts()
        fakeValidator = await setupFakeValidator()
      })

      it('should revert when issuerId is not valid', async () => {
        // Hack: fake validator can addAccountId directly without contractManager
        // UNREACHABLE: issuerId is already checked by validator
        await expect(
          issuer.connect(fakeValidator).addAccountId(BASE.ISSUER.EMPTY.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it("should revert when issuerId doesn't exist", async () => {
        // Hack: fake validator can addAccountId directly without contractManager
        // UNREACHABLE: issuerId is already checked by validator
        await expect(
          issuer.connect(fakeValidator).addAccountId(NOT_REG_ISSUER_ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })
    })

    describe("accountId doesn't exist", () => {
      it('should revert when accountId is not exist', async () => {
        // Hack: fake validator can addAccountId directly without contractManager
        // UNREACHABLE: accountId is already checked by validator
        const fakeValidator = await setupFakeValidator()
        await expect(
          issuer.connect(fakeValidator).addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_EXIST)
      })
    })
  })
})
