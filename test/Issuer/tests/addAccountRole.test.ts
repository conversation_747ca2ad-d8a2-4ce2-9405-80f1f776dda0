import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  AddAccountRoleOption,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import _ from 'lodash'
import '@nomicfoundation/hardhat-chai-matchers'
import { before } from 'mocha'

describe('addAccountRole()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer, validator, provider, account } = await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  const setupIssuerRole = async () => {
    await issuerFuncs.addIssuerRole({ issuer, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupIssuerRole()
      })

      it('roleが追加できること', async () => {
        const params: AddAccountRoleOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          accountEoa: await accounts[BASE.EOA.ACCOUNT].getAddress(),
        }

        const tx = await issuerFuncs.addAccountRole({ issuer, accounts, options: params })
        // 検証項目の値を取得するイベント関数がないためイベントの検証のみ行う
        await expect(tx)
          .to.emit(account, 'AddAccountRole')
          .withArgs(...Object.values(_.pick(params, ['accountId', 'accountEoa'])), anyValue)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
      })

      it('issuerRoleがない場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addAccountRole({ issuer, accounts })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })

    describe('issuerRoleが登録されている状態', () => {
      before(async () => {
        await setupIssuerRole()
      })

      it('issuerと紐付いていないaccountの場合、エラーがスローされること', async () => {
        const result = issuerFuncs.addAccountRole({
          issuer,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT7.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空issuerIdを指定した場合、エラーが返されること', async () => {
        const result = issuerFuncs.addAccountRole({
          issuer,
          accounts,
          options: { issuerId: BASE.ISSUER.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('空accountIdを指定した場合、エラーが返されること', async () => {
        const result = issuerFuncs.addAccountRole({
          issuer,
          accounts,
          options: { accountId: BASE.ACCOUNT.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        const result = issuerFuncs.addAccountRole({ issuer, accounts, options: { deadline: exceededDeadline } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名エラーがスローされること', async () => {
        const result = issuerFuncs.addAccountRole({ issuer, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('should revert when _hasAccount return false', async () => {
        // Hack: fake validator can addAccountId directly without contract
        // This case mean Issuer add id by faker but not by validator
        const validatorAddress = await validator.getAddress()
        await helpers.setBalance(validatorAddress, 100n ** 18n)
        await helpers.impersonateAccount(validatorAddress) // impersonate validator
        const fakeValidator = await ethers.getSigner(validatorAddress)
        await issuer
          .connect(fakeValidator)
          .addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT7.ID, BASE.TRACE_ID)
        await helpers.stopImpersonatingAccount(validatorAddress) // stop impersonating
        const result = issuerFuncs.addAccountRole({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT7.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('Eoaが空である場合はError', async () => {
        const result = issuerFuncs.addAccountRole({
          issuer,
          accounts,
          options: { accountEoa: '******************************************' },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })
})
