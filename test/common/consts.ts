import web3 from 'web3'
import { toAddress, toBytes, toBytes32 } from './utils'

export const BASE = {
  EOA: {
    ADMIN: 0,
    PROV1: 1, //プロバイダ権限
    PROV2: 2, //プロバイダ権限
    ISSUER1: 3,
    ISSUER2: 4,
    ACCOUNT: 6,
    VALID1: 9,
    VALID2: 5,
  },
  APP: {
    VERSION: 'v1',
  },
  STATUS: {
    APPLYING: toBytes32('applying'),
    ACTIVE: toBytes32('active'),
    FROZEN: toBytes32('frozen'),
    TERMINATING: toBytes32('terminating'),
    TERMINATED: toBytes32('terminated'),
    FORCE_BURNED: toBytes32('force_burned'),
  },
  EMPTY: {
    EOA: toAddress(''),
    ID: toBytes32(''),
    NAME: toBytes32(''),
    ZONE_ID: 0,
  },
  PROV: {
    PROV0: { ID: toBytes32('x100'), NAME: toBytes32('Provider100') },
    PROV1: { ID: toBytes32('x101'), NAME: toBytes32('Provider101') },
    EMPTY: { ID: toBytes32(''), NAME: toBytes32('') },
    EOA_ADDRESS: '0x70997970C51812dc3A010C7d01b50e0d17dc79C8',
  },
  VALID: {
    VALID0: { ID: toBytes32('x200'), NAME: toBytes32('Validator0') },
    VALID1: { ID: toBytes32('x201'), NAME: toBytes32('Validator1') },
    VALID2: { ID: toBytes32('x202'), NAME: toBytes32('Validator2') },
    VALID3: { ID: toBytes32('x203'), NAME: toBytes32('Validator3') },
    VALID4: { ID: toBytes32('x204'), NAME: toBytes32('Validator4') },
    VALID5: { ID: toBytes32('x205'), NAME: toBytes32('Validator5') },
    VALID6: { ID: toBytes32('x206'), NAME: toBytes32('Validator6') },
    VALID7: { ID: toBytes32('x207'), NAME: toBytes32('Validator7') },
    VALID8: { ID: toBytes32('x208'), NAME: toBytes32('Validator8') },
    VALID9: { ID: toBytes32('x209'), NAME: toBytes32('Validator9') },
    VALID10: { ID: toBytes32('x210'), NAME: toBytes32('Validator10') },
    VALID11: { ID: toBytes32('x211'), NAME: toBytes32('Validator11') },
    VALID12: { ID: toBytes32('x212'), NAME: toBytes32('Validator12') },
    VALID13: { ID: toBytes32('x213'), NAME: toBytes32('Validator13') },
    VALID14: { ID: toBytes32('x214'), NAME: toBytes32('Validator14') },
    VALID15: { ID: toBytes32('x215'), NAME: toBytes32('Validator15') },
    VALID16: { ID: toBytes32('x216'), NAME: toBytes32('Validator16') },
    VALID17: { ID: toBytes32('x217'), NAME: toBytes32('Validator17') },
    VALID18: { ID: toBytes32('x218'), NAME: toBytes32('Validator18') },
    VALID19: { ID: toBytes32('x219'), NAME: toBytes32('Validator19') },
    VALID20: { ID: toBytes32('x220'), NAME: toBytes32('Validator20') },
    UNREGISTERED_VALID: { ID: toBytes32('x299'), NAME: toBytes32('UnregisteredValidator') },
    EOA_ADDRESS: '0x70997970C51812dc3A010C7d01b50e0d17dc79C8',
    EMPTY: { ID: toBytes32(''), NAME: toBytes32('') },
  },
  TOKEN: {
    TOKEN1: {
      ID: toBytes32('x1000'),
      NAME: toBytes32('token'),
      SYMBOL: toBytes32('1'),
      TOTAL_SUPPLY: 1000000,
    },
    TOKEN2: {
      ID: toBytes32('x2000'),
      NAME: toBytes32('token2'),
      SYMBOL: toBytes32('2'),
      TOTAL_SUPPLY: 3000000,
    },
    EMPTY: toBytes32(''),
    TRANSFER_TYPE: {
      TRANSFER: toBytes32('transfer'),
      CUSTOM_TRANSFER: toBytes32('custom_transfer'),
      CHARGE: toBytes32('charge'),
      DISCHARGE: toBytes32('discharge'),
      DISCHARGE_FROM_FIN: toBytes32('discharge_from_fin'),
    },
  },
  REToken: {
    TOTAL_SUPPLY: 4,
    TOKENS: {
      TOKEN1: {
        TOKEN_ID_BYTES32: String(toBytes32('1')),
        TOKEN_ID_STRING: '1',
        METADATA_ID: toBytes32('123'),
        METADATA_HASH: toBytes32('0x001'),
      },
      TOKEN2: {
        TOKEN_ID_BYTES32: String(toBytes32('2')),
        TOKEN_ID_STRING: '2',
        METADATA_ID: toBytes32('124'),
        METADATA_HASH: toBytes32('0x002'),
      },
      TOKEN3: {
        TOKEN_ID_BYTES32: String(toBytes32('3')),
        TOKEN_ID_STRING: '3',
        METADATA_ID: toBytes32('123'),
        METADATA_HASH: toBytes32('0x001'),
      },
      TOKEN4: {
        TOKEN_ID_BYTES32: String(toBytes32('4')),
        TOKEN_ID_STRING: '4',
        METADATA_ID: toBytes32('124'),
        METADATA_HASH: toBytes32('0x002'),
      },
    },
    EMPTY: toBytes32(''),
  },
  ISSUER: {
    ISSUER0: { ID: toBytes32('x300'), BANK_CODE: 9999, NAME: 'DECCURET_ISSUER' },
    ISSUER1: { ID: toBytes32('x301'), BANK_CODE: 1001, NAME: 'Issuer1' },
    ISSUER2: { ID: toBytes32('x302'), BANK_CODE: 1002, NAME: 'Issuer2' },
    ISSUER3: { ID: toBytes32('x303'), BANK_CODE: 1003, NAME: 'Issuer3' },
    ISSUER4: { ID: toBytes32('x304'), BANK_CODE: 1004, NAME: 'Issuer4' },
    ISSUER5: { ID: toBytes32('x305'), BANK_CODE: 1005, NAME: 'Issuer5' },
    ISSUER6: { ID: toBytes32('x306'), BANK_CODE: 1006, NAME: 'Issuer6' },
    ISSUER7: { ID: toBytes32('x307'), BANK_CODE: 1007, NAME: 'Issuer7' },
    ISSUER8: { ID: toBytes32('x308'), BANK_CODE: 1008, NAME: 'Issuer8' },
    ISSUER9: { ID: toBytes32('x309'), BANK_CODE: 1009, NAME: 'Issuer9' },
    ISSUER10: { ID: toBytes32('x310'), BANK_CODE: 1010, NAME: 'Issuer10' },
    ISSUER11: { ID: toBytes32('x311'), BANK_CODE: 1011, NAME: 'Issuer11' },
    ISSUER12: { ID: toBytes32('x312'), BANK_CODE: 1012, NAME: 'Issuer12' },
    ISSUER13: { ID: toBytes32('x313'), BANK_CODE: 1013, NAME: 'Issuer13' },
    ISSUER14: { ID: toBytes32('x314'), BANK_CODE: 1014, NAME: 'Issuer14' },
    ISSUER15: { ID: toBytes32('x315'), BANK_CODE: 1015, NAME: 'Issuer15' },
    ISSUER16: { ID: toBytes32('x316'), BANK_CODE: 1016, NAME: 'Issuer16' },
    ISSUER17: { ID: toBytes32('x317'), BANK_CODE: 1017, NAME: 'Issuer17' },
    ISSUER18: { ID: toBytes32('x318'), BANK_CODE: 1018, NAME: 'Issuer18' },
    ISSUER19: { ID: toBytes32('x319'), BANK_CODE: 1019, NAME: 'Issuer19' },
    ISSUER20: { ID: toBytes32('x320'), BANK_CODE: 1020, NAME: 'Issuer20' },
    ISSUER21: { ID: toBytes32('x321'), BANK_CODE: 1021, NAME: 'Issuer21' },
    ISSUER22: { ID: toBytes32('x322'), BANK_CODE: 1022, NAME: 'Issuer22' },
    ISSUER23: { ID: toBytes32('x323'), BANK_CODE: 1023, NAME: 'Issuer23' },
    ISSUER24: { ID: toBytes32('x324'), BANK_CODE: 1024, NAME: 'Issuer24' },
    ISSUER25: { ID: toBytes32('x325'), BANK_CODE: 1025, NAME: 'Issuer25' },
    DECURRET_ISSUER: { ID: toBytes32('x326'), BANK_CODE: 9999, NAME: 'DecurretIssuer' },
    EMPTY: { ID: toBytes32(''), BANK_CODE: 0, NAME: '' },
  },
  ACCOUNT: {
    ACCOUNT0: { ID: toBytes32('x400'), NAME: 'ACCOUNT0' },
    ACCOUNT1: { ID: toBytes32('x401'), NAME: 'ACCOUNT1' },
    ACCOUNT2: { ID: toBytes32('x402'), NAME: 'ACCOUNT2' },
    ACCOUNT3: { ID: toBytes32('x403'), NAME: 'ACCOUNT3' },
    ACCOUNT4: { ID: toBytes32('x404'), NAME: 'ACCOUNT4' },
    ACCOUNT5: { ID: toBytes32('x405'), NAME: 'ACCOUNT5' },
    ACCOUNT6: { ID: toBytes32('x406'), NAME: 'ACCOUNT6' },
    ACCOUNT7: { ID: toBytes32('x407'), NAME: 'ACCOUNT7' },
    ACCOUNT8: { ID: toBytes32('x408'), NAME: 'ACCOUNT8' },
    ACCOUNT9: { ID: toBytes32('x409'), NAME: 'ACCOUNT9' },
    ACCOUNT10: { ID: toBytes32('x410'), NAME: 'ACCOUNT10' },
    ACCOUNT11: { ID: toBytes32('x411'), NAME: 'ACCOUNT11' },
    ACCOUNT12: { ID: toBytes32('x412'), NAME: 'ACCOUNT12' },
    ACCOUNT13: { ID: toBytes32('x413'), NAME: 'ACCOUNT13' },
    ACCOUNT14: { ID: toBytes32('x414'), NAME: 'ACCOUNT14' },
    ACCOUNT15: { ID: toBytes32('x415'), NAME: 'ACCOUNT15' },
    ACCOUNT16: { ID: toBytes32('x416'), NAME: 'ACCOUNT16' },
    ACCOUNT17: { ID: toBytes32('x417'), NAME: 'ACCOUNT17' },
    ACCOUNT18: { ID: toBytes32('x418'), NAME: 'ACCOUNT18' },
    ACCOUNT19: { ID: toBytes32('x419'), NAME: 'ACCOUNT19' },
    ACCOUNT20: { ID: toBytes32('x420'), NAME: 'ACCOUNT20' },
    EMPTY: { ID: toBytes32(''), NAME: '' },
    EOA_ADDRESS: '0x90F79bf6EB2c4f870365E785982E1f101E93b906',
  },
  ZONE_ID: {
    EMPTY_ID: 0,
    ID0: 3000,
    ID1: 3001,
    ID2: 3002,
  },
  ZONE_NAME: {
    EMPTY_NAME: '',
    NAME0: 'FIN1',
    NAME1: 'BIZ1',
    NAME2: 'BIZ2',
  },
  REASON_CODE1: toBytes32(''),
  REASON_CODE2: toBytes32('1'),
  LIMIT_AMOUNTS: [1000, 2000, 3000, 4000, 5000, 5000],
  LIMIT_VALUES: {
    mint: 3000,
    burn: 4000,
    charge: 2000,
    discharge: 4000,
    transfer: 1000,
    cumulative: {
      total: 5000,
      mint: 1000,
      burn: 1000,
      charge: 1000,
      discharge: 1000,
      transfer: 800,
    },
  },
  MAX_APPROVAL_VALUE: ***************,
  ZERO_APPROVAL_VALUE: 0,
  ROLE: {
    EMPTY: toBytes32(''),
    ADMIN: web3.utils.keccak256('ADMIN_ROLE'),
    PROV: web3.utils.keccak256('PROV_ROLE'),
    ISSURE: web3.utils.keccak256('ISSUER_ROLE'),
  },
  CALC_PATTERN: {
    ADD: 1,
    SUB: 2,
  },
  TRACE_ID: toBytes32('x123456'),
  MEMO: 'テスト摘要',
  SALTS: {
    SET_PROVIDER_ALL: 'setProviderAll',
    GET_PROVIDER_ALL: 'getProviderAll',
    SET_ISSUERS_ALL: 'setIssuersAll',
    GET_ISSUERS_ALL: 'getIssuersAll',
    SET_VALIDATORS_ALL: 'setValidatorsAll',
    GET_VALIDATORS_ALL: 'getValidatorsAll',
    GET_ACCOUNTS_ALL: 'getAccountsAll',
    SET_ACCOUNTS_ALL: 'setAccountsAll',
    GET_FINACCOUNTS_ALL: 'getFinAccountsAll',
    SET_FINACCOUNTS_ALL: 'setFinAccountsAll',
    GET_BIZACCOUNTS_ALL: 'getBizAccountsAll',
    SET_BIZACCOUNTS_ALL: 'setBizAccountsAll',
    GET_ZONEIDS_ALL: 'getZoneIdsAll',
    SET_ZONEIDS_ALL: 'setZoneIdsAll',
    GET_TOKEN_ALL: 'getTokenAll',
    SET_TOKEN_ALL: 'setTokenAll',
    GET_RETOKEN_ALL: 'getRETokensAll',
    SET_RETOKEN_ALL: 'setRETokensAll',
  },
  ACCOUNT_SIG_MSG: {
    TERMINATE: toBytes32('terminate'),
    SYNCHRONOUS: toBytes32('synchronous'),
    TRANSFER: toBytes32('transfer'),
    APPROVE: toBytes32('approve'),
    EXCHANGE: toBytes32('exchange'),
  },
  PK: {
    PK1: '0x2569d5e120b10d696933074eecb9182719fb3a89b7714bc99037d8416b391436',
    PK2: '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6',
    PK3: '0x70f1384b24df3d2cdaca7974552ec28f055812ca5e4da7a0ccd0ac0f8a4a9b00',
    PK4: '0xae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f',
    VALID_PK: '0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d',
  },
  // Order of G
  NN: '0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141',
  TIMEOUT: 100,
  TEST_BALANCES: {
    BALANCE_100: 100,
    BALANCE_150: 150,
    BALANCE_200: 200,
    BALANCE_300: 300,
    BALANCE_400: 400,
    BALANCE_450: 450,
    BALANCE_500: 500,
    BALANCE_600: 600,
    BALANCE_800: 800,
    BALANCE_900: 900,
    BALANCE_1000: 1000,
    AMOUNT_25: 25,
    AMOUNT_50: 50,
    AMOUNT_100: 100,
    AMOUNT_200: 200,
    AMOUNT_250: 250,
    AMOUNT_650: 650,
  },
  IBCAPP_NAME: {
    JPY_TOKEN_TRANSFER: 'JPYTokenTransferBridge',
    ACCOUNT_SYNC: 'AccountSyncBridge',
    BALANCE_SYNC: 'BalanceSyncBridge',
  },
  BRIDGE: {
    // Account Id
    ESCROW_ACCOUNT: toBytes32('x3000'),
    ACCOUNT_A: toBytes32('x4000'),
    ACCOUNT_B: toBytes32('x4001'),
    ACCOUNT_A_NAME: 'accountA',
    ACCOUNT_A_STATUS: toBytes32('active'),
    ACCOUNT_B_NAME: 'accountB',
    _NO_ACCOUNT: String('0x').padEnd(66, '0'),

    // ValidatorId
    VALIDATOR_ID: toBytes32('x5000'),
    APPROVE_AMOUNT: 1000,
    EXCHANGE_AMOUNT: 1000,
    AMOUNT: 1000,
    MISC_VALUE_1: toBytes32('0'),
    MISC_VALUE_2: toBytes32('0'),
    MEMO: 'balance',
  },
  EMPTY_ADDRESS: '0x0000000000000000000000000000000000000000',
  ACC_SYNC_BRIDGE: {
    PORT: 'account-sync',
    CHANNEL: 'channel-0',
    VERSION: 'account-sync-1',
  },
  BALANCE_SYNC_BRIDGE: {
    PORT: 'balance-sync',
    CHANNEL: 'channel-1',
    VERSION: 'balance-sync-1',
  },
  TOKEN_TRANS_BRIDGE: {
    PORT: 'token-transfer',
    CHANNEL: 'channel-2',
    VERSION: 'token-transfer-1',
  },
  SALT: {
    ACCOUNT_SYNC: toBytes32('accountSync'),
    BALANCE_SYNC: toBytes32('balanceSync'),
    TOKEN_TRANSFER: toBytes32('tokenTransfer'),
  },
  ZONE: {
    FIN: 3000,
    BIZ: 3001,
    FIN_NAME: 'finzone',
    BIZ_NAME: 'bizzone',
  },
  IBC: {
    PACKET0: toBytes32('test001'),
    ACK: toBytes('success'),
    INVALID_ACK: toBytes('failed'),
    SEQUENCES: {
      START_0: 0,
      START_1: 1,
      START_2: 2,
      END_0: 0,
      END_1: 1,
      END_2: 2,
      END_3: 3,
      END_4: 4,
      END_5: 5,
    },
  },
  REASON_CODE: {
    REASON_CODE1: toBytes32(''),
    REASON_CODE2: toBytes32('1'),
  },
  TIMEOUT_HEIGHT: 1000000,
  MISC_VALUE2_4097:
    '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',
  STRING_UTILS: {
    // テスト用の文字列
    STRING_HELLO_WORLD: 'Hello, World!',
    STRING_THREE_WORDS: 'decurret,dcjpy,amic',
    STRING_LEADING_DELIMITER: ',decurret,dcjpy,amic',
    STRING_TRAILING_DELIMITER: 'decurret,dcjpy,amic,',
    STRING_CONSECUTIVE_DELIMITERS: 'decurret,,dcjpy,,,amic',
    STRING_SAMPLE: 'decurret',
    STRING_EMPTY: '',
    STRING_LONG_32: 'a'.repeat(32),
    STRING_LONG_33: 'a'.repeat(33),
    STRING_THREE_WORDS_JAPANESE: 'ディーカレット・ディーシージェーピーワイ・アミック',
    STRING_MULTIBYTE: 'こんにちは世界',

    // デリミタ
    DELIMITER_COMMA: ',',
    DELIMITER_EMPTY: '',
    DELIMITER_LONG: 'longdelimiter',
    DELIMITER_JAPANESE: '・',

    // インデックス
    INDEX_START_HELLO: 7,
    INDEX_END_WORLD: 12,
    INDEX_START_ZERO: 0,
    INDEX_END_TWO: 2,
    INDEX_START_TWO: 2,
    INDEX_END_FIVE: 5,
    INDEX_START_FIFTEEN: 15,
    INDEX_END_TWENTYONE: 21,
  },
  // Sync
  SYNC: {
    _SYNC_MINT: toBytes32('syncMint'),
    _SYNC_BURN: toBytes32('syncBurn'),
    _SYNC_CHARGE: toBytes32('syncCharge'),
    _SYNC_DISCHARGE: toBytes32('syncDischarge'),
    _SYNC_TRANSFER: toBytes32('syncTransfer'),
  },
}

export const ERR = {
  ACTRL: {
    // ACTRL_BEGIN: "1000",
    ACTRL_SIG_TIMEOUT: '1001:sig timeout',
    ACTRL_BAD_ROLE: '1003:bad role',
    ACTRL_BAD_SIG: '1004:bad sig',
    ACTRL_NOT_ROLE: '1101:not role',
    ACTRL_INVALID_VAL: '1992:accessctrl invalid value',
    ACTRL_NOT_ADMIN_ROLE: '1995:accessctrl not admin',
    // ACTRL_END: "1999",
  },
  PROV: {
    // PROV_BEGIN: "2000",
    PROV_ID_EXIST: '2001:exist providerID',
    PROV_ID_NOT_EXIST: '2002:not exist',
    PROV_ADDR_EXIST: '2003:exist addr',
    PROV_DISABLED: '2004:disabled',
    PROV_CURRID_EXIST: '2005:exist currID',
    NOT_PROVIDER_ID: '2006:not provider id',
    PROV_NOT_ROLE: '2101:not prov',
    PROV_NOT_EXIST: '2102:provider not exist',
    ZONE_NOT_EXIST: '2103:zone not exist',
    NOT_PROVIDER_CONTRACT: '2104:not provider contract',
    PROV_INVALID_VAL: '2992:provider invalid value',
    PROV_NOT_ADMIN_ROLE: '2995:provider not admin',
    // PROV_END: "2999",
  },
  ISSUER: {
    // ISSUER_BEGIN: "3000",
    ISSUER_ID_EXIST: '3001:exist id',
    ISSUER_ID_NOT_EXIST: '3002:not exist',
    ISSUER_ADDR_EXIST: '3003:exist addr',
    ISSUER_DISABLED: '3004:disabled',
    ISSUER_ACCOUNT_EXIST: '3005:exist userID',
    ISSUER_TOKEN_EXIST: '3006:exist tokenID',
    ISSUER_HAS_THIS_ACCOUNT_ID: '3007:exist accountID',
    ITEMFLGS_INVALID_VAL: '3008:itemFlgs invalid count',
    LIMITAMOUNT_INVALID_VAL: '3009:limitAmount invalid count',
    ISSUER_EXIST_BANK_CODE: '3010:exist bank code',
    ISSUER_NOT_ROLE: '3101:not issuer',
    ISSUER_OUT_OF_INDEX: '3102:out of index',
    ISSUER_TOO_LARGE_LIMIT: '3103:too large limit',
    ISSUER_OFFSET_OUT_OF_INDEX: '3104:out of index (offset)',
    NOT_ISSUER_CONTRACT: '3105:not issuer contract',
    ISSUER_INVALID_VAL: '3992:issuer invalid value',
    ISSUER_NOT_ADMIN_ROLE: '3995:issuer not admin',
    ISSUER_NOT_DECURRET: '3999:issuer not decurret',
    // ISSUER_END: "3999",
  },
  ACCOUNT: {
    // ACCOUNT_BEGIN: "6000",
    ACCOUNT_ID_EXIST: '6001:exist id',
    ACCOUNT_ID_NOT_EXIST: '6002:not exist',
    ACCOUNT_ADDR_EXIST: '6003:exist addr',
    ACCOUNT_DISABLED: '6004:disabled',
    OWNER_NOT_EXIST: '6005:owner not exist',
    SPENDER_NOT_EXIST: '6006:spender not exist',
    ALLOWANCE_NOT_ENOUGH: '6007:allowance not enough',
    BALANCE_NOT_ENOUGH: '6008:balance not enough',
    ACCOUNT_NOT_IDENTIFIED: '6009:account not identified',
    ACCOUNT_TERMINATED: '6010:terminated account',
    ACCOUNT_BALANCE_EXIST: '6011:balance exist account',
    ACCOUNT_NOT_FROZEN: '6012:account not frozen',
    ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED: '6016:account not frozen or force burned',
    ACCOUNT_NOT_ROLE: '6101:not user',
    ACCOUNT_OUT_OF_INDEX: '6102:out of index',
    ACCOUNT_PROV_NOT_ROLE: '6103:not prov',
    ACCOUNT_TOO_LARGE_LIMIT: '6104:too large limit',
    ACCOUNT_OFFSET_OUT_OF_INDEX: '6105:out of index (offset)',
    ACCOUNT_INVALID_VAL: '6992:account invalid value',
    ACCOUNT_OVERFLOW: '6993:account overflow',
    ACCOUNT_UNDERFLOW: '6994:account underflow',
    ACCOUNT_EXCEED_REGISTER_LIMIT: '6995:exceed account register limit',
    ACCOUNT_BAD_SIG: '6996:bad account sig',
    ACCOUNT_INVALID_SIG: '6997:bad account sig',
    NOT_ACCOUNT_CONTRACT: '7106:not account contract',
    ACCOUNT_NOT_FORCE_BURNED: '6014:account not force burned',
    ACCOUNT_EXCEED_APPROVAL_LIMIT: '6015:exceed approval limit',
    ACCOUNT_TERMINATING_OR_TERMINATED: '6016:account already termiating or terminated',
    ACCOUNT_BALANCE_NOT_ZERO: '6013:account balance not zero',
    ACCOUNT_INVALID_BURNED_AMOUNT: '6018:invalid burned amount',
    ACCOUNT_INVALID_BURNED_BALANCE: '6019:invalid burned balance',
    ACCOUNT_NOT_ADMIN: '6998:account not admin',
    ACCOUNT_STATUS_INVALID: '20004:account status is invalid',
    SEND_ACCOUNT_IS_INVALID_VALUE: '6200:send account is invalid value',
    FROM_ACCOUNT_IS_INVALID_VALUE: '6201:from account is invalid value',
    TO_ACCOUNT_IS_INVALID_VALUE: '6202:to account is invalid value',
    SEND_ACCOUNT_IS_NOT_EXIST: '6203:send account is not exist',
    FROM_ACCOUNT_IS_NOT_EXIST: '6204:from account is not exist',
    TO_ACCOUNT_IS_NOT_EXIST: '6205:to account is not exist',
    SEND_ACCOUNT_STATUS_IS_DISABLED: '6206:send account status is disabled',
    FROM_ACCOUNT_STATUS_IS_DISABLED: '6207:from account status is disabled',
    TO_ACCOUNT_STATUS_IS_DISABLED: '6208:to account status is disabled',
    FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT: '6209:from and to account issuers are different',
    // ACCOUNT_END: "6999",
  },
  FINACCOUNT: {
    LIMIT_AMOUNT_INVALID_COUNT: '11005:limitAmount invalid count',
    EXCEEDED_DAILY_LIMIT: '11014:exceeded daily limit',
    EXCEEDED_MINT_LIMIT: '11015:exceeded mint limit',
    EXCEEDED_BURN_LIMIT: '11016:exceeded burn limit',
    EXCEEDED_CHARGE_LIMIT: '11018:exceeded charge limit',
    EXCEEDED_TRANSFER_LIMIT: '11017:exceeded transfer limit',
    EXCEEDED_DISCHARGE_LIMIT: '11021:exceeded discharge limit',
    EXCEEDED_DAILY_MINT_LIMIT: '11022:exceeded daily mint limit',
    EXCEEDED_DAILY_BURN_LIMIT: '11023:exceeded daily burn limit',
    EXCEEDED_DAILY_CHARGE_LIMIT: '11024:exceeded daily charge limit',
    EXCEEDED_DAILY_DISCHARGE_LIMIT: '11025:exceeded daily discharge limit',
    EXCEEDED_DAILY_TRANSFER_LIMIT: '11026:exceeded daily transfer limit',
  },
  TOKEN: {
    // TOKEN_BEGIN: "7000",
    TOKEN_ID_EXIST: '7001:exist token id',
    TOKEN_ID_NOT_EXIST: '7002:token id not exist',
    TOKEN_ADDR_EXIST: '7003:exist token addr',
    TOKEN_DISABLED: '7004:token disabled',
    TOKEN_BALANCE_NOT_ENOUGH: '7006:balance not enough',
    TOKEN_ACCOUNT_DISABLED: '7007:disabled account',
    TOKEN_ACCOUNT_UNIDENTIFIED: '7008:not identified account',
    TOKEN_APPROVE: '7009:approve',
    TOKEN_ISSUER_UNKNOWN: '7010:unknown',
    NOT_TOKEN_ID: '7011:not token id',
    TOKEN_NOT_EXIST: '7011:token not exist',
    TOKEN_ZERO_AMOUNT: '7012:token zero amount',
    TOKEN_NOT_ROLE: '7101:not token',
    TOKEN_OUT_OF_INDEX: '7102:out of index',
    TOKEN_ISSUER_NOT_ROLE: '7103:not issuer',
    TOKEN_CURR_NOT_ROLE: '7104:not currency',
    NOT_TOKEN_CONTRACT: '7105:not token contract',
    NOT_ACCOUNT_CONRACT: '7106:not account contract',
    TOKEN_INVALID_VAL: '7992:token invalid value',
    // TODO: overflow, underflowは言語仕様としてチェックされるので不要
    TOKEN_OVERFLOW: '7993:token overflow',
    TOKEN_UNDERFLOW: '7994:token underflow',
    // TOKEN_END: "7999",
    INVALID_ORGANIZATION_ID: '20001:organization id is invalid',
    INVALID_ACCOUNT_ID: '20002:account id is invalid',
    INVALID_CALLER_ADDRESS: '20004:caller is different',
  },
  RETOKEN: {
    RETOKEN_TOO_LARGE_LIMIT: '12100:retoken too large limit',
    RETOKEN_OFFSET_OUT_OF_INDEX: '12101:retoken out of index (offset)',
    TOKEN_EXCEED_REGISTER_LIMIT: '12102:exceed token register limit',
    RETOKEN_NOT_EXIST: '12103:retoken not exist',
    RETOKEN_NOT_OWNER: '12104:retoken not owner',
    RETOKEN_NOT_ACTIVE: '12105:retoken not active',
    RETOKEN_IS_LOCKED: '12106:retoken is locked',
    RETOKEN_FROM_TO_ARE_SAME: '12108:retoken from to are same',
    RETOKEN_INVALID_VAL: '12109:retoken invalid val',
    RETOKEN_ID_EXIST: '12109:retoken id exist',
    RETOKEN_INVALID_MISC1: '12110:retoken invalid misc1',
    RETOKEN_INVALID_MISC2: '12111:retoken invalid misc2',
  },
  VALID: {
    // VALID_BEGIN: "8000",
    VALIDATOR_ID_EXIST: '8001:exist id',
    VALIDATOR_ID_NOT_EXIST: '8002:not exist',
    VALIDATOR_ADDR_EXIST: '8003:exist addr',
    VALIDATOR_DISABLED: '8004:disabled',
    VALIDATOR_ACCOUNT_NOT_EXIST: '8005:validator account not exist',
    VALIDATOR_NOT_ROLE: '8101:not valid',
    VALIDATOR_OUT_OF_INDEX: '8102:out of index',
    VALIDATOR_TOO_LARGE_LIMIT: '8103:too large limit',
    VALIDATOR_OFFSET_OUT_OF_INDEX: '8104:out of index (offset)',
    NOT_VALIDATOR_CONTRACT: '8105:not validator contract',
    VALIDATOR_INVALID_VAL: '8992:validator invalid value',
    VALIDATOR_NOT_ADMIN_ROLE: '8995:validator not admin',
    FROM_TO_SAME: '8999:from to are same',
    // VALID_END: "8999",
  },
  IBC: {
    // IBC_BEGIN: "9000",
    IBC_SENDER_NOT_AUTH: '9001:sender not authorized',
    IBC_PACKET_TIMED_OUT: '9002:packet timed out',
    IBC_PACKET_ALWAYS_RECV: '9003:packet always received',
    NOT_IBC_CONTRACT: '9004:not ibc contract',
    IBC_APP_JPYTT_ESCROW_NOT_REG: '9101:escrow not registered',
    IBC_APP_JPYTT_ESCROW_ALWAYS_REG: '9102:escrow always registered',
    IBC_APP_JPYTT_EXTERNAL_CONTRACT_ERR: '9103:external contract error',
    IBC_INVALID_VAL: '9992:IBC invalid value',
    IBC_NOT_ADMIN_ROLE: '9995:IBC not admin',
    FIN_ZONE_OP_NOT_ALLOWED: '09999:Fin zone operation denied',
    NOT_ALLOWED_FROM_FIN_ZONE: '09999:Not allowed from financial zone',
    // VALID_END: "9999",
  },
  TRANSFERPROXY: {
    // TRANSFERPROXY_BEGIN: "10000",
    CUSTOM_CONTRACT_NOT_EXIST: '10001:custom contract not exist',
    CUSTOM_CONTRACT_EXIST: '10002:custom contract already exist',
    // TRANSFERPROXY_END: "10999",
  },
  COMMON: {
    // Common_BEGIN: "11000";
    EXCEEDED_REGISTRATION_LIMIT: '11000:exceeded registration limit',
    // Common_END: "11999";
    INVALID_ACCOUNT_ID: '20002:account id is invalid',
    CALLER_NOT_IBC: 'IBCAppBase: caller is not the IBC contract',
    VER_NOT_MATCH: 'version mismatch',
    NOT_ADMIN_ROLE: 'not admin role',
    TOO_LONG_TO_CONVERT_BYTE32: 'Input string is too long to convert to bytes32',
  },
  // Oracle
  ORACLE: {
    INVOKER_INVALID_VAL: 'invoker invalid value',
    ORACLE_INVALID_VAL: 'Oracle invalid value',
    NOT_INVOKER_ADDRESS: 'not invoker address',
    WRONG_ARGUMENT_NUMBER: 'wrong argument number',
    ORACLE_VALID_NOT_ROLE: 'not valid',
  },
  // Initializer
  INITIALIZER: {
    ALREADY_INIT: 'Initializable: contract is already initialized',
  },
}
