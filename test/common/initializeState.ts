import { Provider } from '@contracts/Provider'
import hre, { ethers } from 'hardhat'
import { getDeadline } from './utils'
import * as privateKey from '@/privateKey'
import {} from '@/types/hardhat'
import '@nomicfoundation/hardhat-ethers'

export const deployFixture = async () => {
  const [admin] = await hre.ethers.getSigners()

  // Deploy storage logic
  const ProviderLib = await (await hre.ethers.getContractFactory('ProviderLib')).deploy()
  const IssuerLib = await (await hre.ethers.getContractFactory('IssuerLib')).deploy()
  const ValidatorLib = await (await hre.ethers.getContractFactory('ValidatorLib')).deploy()
  const AccountLib = await (await hre.ethers.getContractFactory('AccountLib')).deploy()
  const FinancialZoneAccountLib = await (await hre.ethers.getContractFactory('FinancialZoneAccountLib')).deploy()
  const BusinessZoneAccountLib = await (await hre.ethers.getContractFactory('BusinessZoneAccountLib')).deploy()
  const TokenLib = await (await hre.ethers.getContractFactory('TokenLib')).deploy()
  const renewableEnergyTokenLogic = await (await hre.ethers.getContractFactory('RenewableEnergyTokenLib')).deploy()

  // Deploy contract
  const remigrationLib = await (await hre.ethers.getContractFactory('RemigrationLib')).deploy()
  const accessCtrl = await (await hre.ethers.getContractFactory('AccessCtrl')).deploy()
  const account = await (
    await hre.ethers.getContractFactory('Account', {
      libraries: {
        AccountLib: await AccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const financialZoneAccount = await (
    await hre.ethers.getContractFactory('FinancialZoneAccount', {
      libraries: {
        FinancialZoneAccountLib: await FinancialZoneAccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const businessZoneAccount = await (
    await hre.ethers.getContractFactory('BusinessZoneAccount', {
      libraries: {
        BusinessZoneAccountLib: await BusinessZoneAccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const contractManager = await (await hre.ethers.getContractFactory('ContractManager')).deploy()
  const issuer = await (
    await hre.ethers.getContractFactory('Issuer', {
      libraries: {
        IssuerLib: await IssuerLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const provider = (await (
    await hre.ethers.getContractFactory('Provider', {
      libraries: {
        ProviderLib: await ProviderLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()) as unknown as Provider
  const token = await (
    await hre.ethers.getContractFactory('Token', {
      libraries: {
        TokenLib: await TokenLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const ibcToken = await (
    await hre.ethers.getContractFactory('IBCToken', {
      libraries: {
        TokenLib: await TokenLib.getAddress(),
      },
    })
  ).deploy()
  const financialCheck = await (await hre.ethers.getContractFactory('FinancialCheck')).deploy()
  const renewableEnergyToken = await (
    await hre.ethers.getContractFactory('RenewableEnergyToken', {
      libraries: {
        RenewableEnergyTokenLib: await renewableEnergyTokenLogic.getAddress(),
      },
    })
  ).deploy()
  const validator = await (
    await hre.ethers.getContractFactory('Validator', {
      libraries: {
        ValidatorLib: await ValidatorLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const stringUtilsMock = await (await hre.ethers.getContractFactory('StringUtilsMock')).deploy()
  const transferProxy = await (await hre.ethers.getContractFactory('TransferProxy')).deploy()
  const customTransfer1 = await (await hre.ethers.getContractFactory('TransferableMock1')).deploy()
  const customTransfer2 = await (await hre.ethers.getContractFactory('TransferableMock2')).deploy()
  const customTransfer3 = await (await hre.ethers.getContractFactory('TransferableMock3')).deploy()
  const oracle = await (await hre.ethers.getContractFactory('Oracle')).deploy()
  const remigrationRestore = await (await hre.ethers.getContractFactory('RemigrationRestore')).deploy()
  const remigrationBackup = await (await hre.ethers.getContractFactory('RemigrationBackup')).deploy()

  // contract-ibc
  const ibcHandler = await (await hre.ethers.getContractFactory('IBCHandlerMock')).deploy()
  const accountSyncBridge = await (await hre.ethers.getContractFactory('AccountSyncBridge')).deploy()
  const balanceSyncBridge = await (await hre.ethers.getContractFactory('BalanceSyncBridge')).deploy()
  const jpyTokenTransferBridge = await (await hre.ethers.getContractFactory('JPYTokenTransferBridge')).deploy()
  const ibcTokenMock = await (await hre.ethers.getContractFactory('IBCTokenMock')).deploy()
  const providerMock = await (await hre.ethers.getContractFactory('ProviderMock')).deploy()
  const validatorMock = await (await hre.ethers.getContractFactory('ValidatorMock')).deploy()
  const accountMock = await (await hre.ethers.getContractFactory('AccountMock')).deploy()
  const accessCtrlMock = await (await hre.ethers.getContractFactory('AccessCtrlMock')).deploy()
  const businessZoneAccountMock = await (await hre.ethers.getContractFactory('BusinessZoneAccountMock')).deploy()

  // Initialize
  const contractManagerAddress = await contractManager.getAddress()
  const accessCtrlAddress = await accessCtrl.getAddress()
  const providerAddress = await provider.getAddress()
  const issuerAddress = await issuer.getAddress()
  const validatorAddress = await validator.getAddress()
  const accountAddress = await account.getAddress()
  const financialZoneAccountAddress = await financialZoneAccount.getAddress()
  const businessZoneAccountAddress = await businessZoneAccount.getAddress()
  const tokenAddress = await token.getAddress()
  const ibcTokenAddress = await ibcToken.getAddress()
  const financialCheckAddress = await financialCheck.getAddress()
  const transferProxyAddress = await transferProxy.getAddress()

  await renewableEnergyToken.getAddress()
  await customTransfer1.getAddress()
  await customTransfer2.getAddress()
  await customTransfer3.getAddress()
  await oracle.getAddress()
  await remigrationLib.getAddress()
  await remigrationRestore.getAddress()
  await remigrationBackup.getAddress()

  const ibcHandlerAddress = await ibcHandler.getAddress()
  const ibcTokenMockAddress = await ibcTokenMock.getAddress()
  const providerMockAddress = await providerMock.getAddress()
  const validatorMockAddress = await validatorMock.getAddress()
  const accountMockAddress = await accountMock.getAddress()
  const accessCtrlMockAddress = await accessCtrlMock.getAddress()
  const businessZoneAccountMockAddress = await businessZoneAccountMock.getAddress()

  await accountSyncBridge.getAddress()
  await balanceSyncBridge.getAddress()
  await jpyTokenTransferBridge.getAddress()
  await stringUtilsMock.getAddress()

  await contractManager.initialize()
  await accessCtrl.initialize(contractManagerAddress, await admin.getAddress())
  await provider.initialize(contractManagerAddress)
  await issuer.initialize(contractManagerAddress)
  await validator.initialize(contractManagerAddress)
  await account.initialize(contractManagerAddress)
  await financialZoneAccount.initialize(contractManagerAddress)
  await businessZoneAccount.initialize(contractManagerAddress)
  await token.initialize(contractManagerAddress)
  await ibcToken.initialize(contractManagerAddress)
  await financialCheck.initialize(contractManagerAddress)
  await renewableEnergyToken.initialize(contractManagerAddress, tokenAddress)
  await transferProxy.initialize(contractManagerAddress, tokenAddress)
  await customTransfer1.initialize(tokenAddress)
  await customTransfer2.initialize(tokenAddress)
  await customTransfer3.initialize(tokenAddress)
  await oracle.initialize()
  await remigrationRestore.initialize(contractManagerAddress)
  await remigrationBackup.initialize(contractManagerAddress)

  await accountSyncBridge.initialize(
    ibcHandlerAddress,
    providerMockAddress,
    validatorMockAddress,
    accessCtrlMockAddress,
    businessZoneAccountMockAddress,
    ibcTokenMockAddress,
  )
  await balanceSyncBridge.initialize(ibcHandlerAddress, ibcTokenMockAddress, accountMockAddress, accessCtrlMockAddress)
  await jpyTokenTransferBridge.initialize(ibcHandlerAddress, ibcTokenMockAddress, accessCtrlMockAddress)

  const deadline = await getDeadline()
  const testPrivateKey = privateKey.key[0]
  const sig = privateKey.sig(
    testPrivateKey,
    [
      'address',
      'address',
      'address',
      'address',
      'address',
      'address',
      'address',
      'address',
      'address',
      'address',
      'address',
      'uint256',
    ],
    [
      accessCtrlAddress,
      providerAddress,
      issuerAddress,
      validatorAddress,
      accountAddress,
      financialZoneAccountAddress,
      businessZoneAccountAddress,
      tokenAddress,
      ibcTokenAddress,
      financialCheckAddress,
      transferProxyAddress,
      deadline,
    ],
  )

  await contractManager.setContracts(
    {
      ctrlAddress: accessCtrlAddress,
      providerAddress: providerAddress,
      issuerAddress: issuerAddress,
      validatorAddress: validatorAddress,
      accountAddress: accountAddress,
      financialZoneAccountAddress: financialZoneAccountAddress,
      businessZoneAccountAddress: businessZoneAccountAddress,
      tokenAddress: tokenAddress,
      ibcTokenAddress: ibcTokenAddress,
      financialCheckAddress: financialCheckAddress,
      transferProxyAddress: transferProxyAddress,
    },
    deadline,
    sig[0],
  )

  const accounts = await ethers.getSigners()

  return {
    accounts,
    accessCtrl,
    account,
    financialZoneAccount,
    businessZoneAccount,
    contractManager,
    issuer,
    provider,
    token,
    ibcToken,
    financialCheck,
    renewableEnergyToken,
    validator,
    transferProxy,
    customTransfer1,
    customTransfer2,
    customTransfer3,
    oracle,
    remigrationBackup,
    remigrationRestore,
    ibcHandler,
    accountSyncBridge,
    balanceSyncBridge,
    jpyTokenTransferBridge,
    ibcTokenMock,
    providerMock,
    validatorMock,
    accountMock,
    accessCtrlMock,
    businessZoneAccountMock,
    stringUtilsMock,
  }
}
