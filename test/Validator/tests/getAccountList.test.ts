import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('getAccountList()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await validatorFuncs.addValidator({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID1.ID,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupAccountData = async () => {
    const appliedTime = '0'
    const registeredTime = '0'
    const terminatingTime = '0'
    const terminatedTime = '0'
    const addAccountParams = [
      {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT2.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT3.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT4.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT5.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT6.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT7.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT8.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT9.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT10.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT11.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
    ]

    for (let i = 0; i < addAccountParams.length; i++) {
      const currentTimestamp = await helpers.time.latest()
      addAccountParams[i].registeredAt = currentTimestamp.toString() // DCPF-25563 Set true timestamp of registeredAt
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          accountId: addAccountParams[i].accountId,
          accountName: addAccountParams[i].accountName,
        },
      })
    }

    return addAccountParams
  }

  describe('正常系', () => {
    describe('accountが登録されていない状態', () => {
      before(async () => {
        await setupFixture()
        await setupBasicRoles()
      })

      it('Accountが登録されていない場合、空リストが取得できること', async () => {
        const offset = 0
        const limit = 10
        const sortOrder = 'asc'
        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, {
          accounts: [],
          totalCount: 0,
          err: '',
        })
      })

      it('should return empty when sortOrder is _DESC_SORT', async () => {
        const offset = 0
        const limit = 10
        const sortOrder = 'desc'

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, {
          accounts: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      let addAccountParams: any[]

      before(async () => {
        await setupFixture()
        await setupBasicRoles()
        addAccountParams = await setupAccountData()
      })

      const appliedTime = '0'
      const terminatingTime = '0'
      const terminatedTime = '0'
      const sortOrder = 'asc'

      const assertList = async (
        result: PromiseType<ReturnType<typeof validatorFuncs.getAccountList>>,
        expected: typeof addAccountParams,
      ) => {
        assert.strictEqual(result.accounts.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          assert.isString(result.accounts[i].accountId, 'accountId')
          utils.assertEqualForEachField(result.accounts[i], {
            accountId: v.accountId,
            accountName: v.accountName,
            balance: v.balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(v.accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: v.reasonCode,
            appliedAt: v.appliedAt,
            registeredAt: v.registeredAt,
            terminatingAt: v.terminatingAt,
            terminatedAt: v.terminatedAt,
          })
        })
      }

      it('複数のgetIssuerList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと', async () => {
        // 同時に異なるoffsetとlimitでリクエストを発行
        const requests = [
          validatorFuncs.getAccountList({ validator, prams: [BASE.VALID.VALID0.ID, 0, 3, sortOrder] }), // 最初の3件
          validatorFuncs.getAccountList({ validator, prams: [BASE.VALID.VALID0.ID, 2, 4, sortOrder] }), // 3番目から次の4件
          validatorFuncs.getAccountList({ validator, prams: [BASE.VALID.VALID0.ID, 5, 5, sortOrder] }), // 6番目から次の5件
        ]

        const results = await Promise.all(requests)

        // 各リクエストの結果を検証
        utils.assertEqualForEachField(results[0], { totalCount: addAccountParams.length, err: '' })
        await assertList(results[0], addAccountParams.slice(0, 3))

        utils.assertEqualForEachField(results[1], { totalCount: addAccountParams.length, err: '' })
        await assertList(results[1], addAccountParams.slice(2, 6))

        utils.assertEqualForEachField(results[2], { totalCount: addAccountParams.length, err: '' })
        await assertList(results[2], addAccountParams.slice(5, 10))
      })

      it('sortOrderにDESCを指定し、offset0, limit20を指定した場合、20要素目から逆順に20件取得できること', async () => {
        const offset = 0
        const limit = 20
        const sortOrder = 'desc'
        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        await assertList(result, addAccountParams.slice().reverse())
      })

      it('offset0, limit10を指定した場合、1要素目から10件取得できること', async () => {
        const offset = 0
        const limit = 10
        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        await assertList(result, addAccountParams.slice(0, 10))
      })

      it('sortOrderにDESCを指定し、offset5, limit10を指定した場合、15要素目から逆順に10件取得できること', async () => {
        const offset = 5
        const limit = 10
        const sortOrder = 'desc'
        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        await assertList(result, addAccountParams.slice().reverse().slice(5, 15))
      })

      it('offset2, limit2を指定した場合、3要素目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        await assertList(result, addAccountParams.slice(2, 4)) // DCPF-25563 corrected the expected result is 2 elements, change slice from (2,3) -> (2,4)
      })

      it('最後の1件が取得できること', async () => {
        // 最後の１件のみ取得
        const offset = addAccountParams.length - 1
        const limit = 1

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        await assertList(result, [addAccountParams[addAccountParams.length - 1]])
      })

      it('limitが取得上限(100件)以下の場合、accountリストが取得できること (limit 100)', async () => {
        // limitが取得上限(100件)以下の場合は検索できる
        const offset = 0
        const limit = 100

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        await assertList(result, addAccountParams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        await assertList(result, [])
      })

      it('limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること', async () => {
        const offset = 0
        const limit = 50

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        await assertList(result, addAccountParams.slice(0, 20))
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        // limitが取得上限(100件)より大きい場合はエラーになる
        const offset = 0
        const limit = 101

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_TOO_LARGE_LIMIT })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        // offsetが指定可能な上限値を超えているためエラー
        const offset = addAccountParams.length + 1
        const limit = 20

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX })
      })

      it('offsetが登録されている件数と同じの場合、エラーが返されること', async () => {
        const offset = addAccountParams.length // 配列の長さと同じ
        const limit = 20

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX })
      })

      it('sortOrderにDESCを指定した場合、accountリストが逆順で取得できること', async () => {
        const offset = 0
        const limit = 21
        const sortOrder = 'desc'

        const currentTimestamp = await helpers.time.latest() // DCPF-25563 correct current timestamp
        const accountParamSmallest = {
          accountId: toBytes32('x399'),
          accountName: toBytes32('ACCOUNT-1'),
          balance: 0,
          accountStatus: 'active',
          reasonCode: BASE.REASON_CODE1,
          appliedAt: appliedTime,
          registeredAt: currentTimestamp.toString(), // DCPF-25563 correct current timestamp
          terminatingAt: terminatingTime,
          terminatedAt: terminatedTime,
        }
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: accountParamSmallest.accountId,
            accountName: accountParamSmallest.accountName,
          },
        })

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length + 1, err: '' })
        addAccountParams.push(accountParamSmallest)
        const listAccountReversed = addAccountParams.slice().reverse()
        await assertList(result, listAccountReversed)
      })
    })
  })
})
