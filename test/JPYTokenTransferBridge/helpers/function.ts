import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { JPYTokenTransferBridgeFunctionType } from './types'
import { genPacket } from './utils'
import privateKey from '@/privateKey'
/**
 * JPYTokenTransferBridgeのイベントを呼ぶ関数を持つobject
 */

export const jpyTokenTransferBridgeFuncs: JPYTokenTransferBridgeFunctionType = {
  setAddress: async ({ jpyTokenTransferBridge, ibcTokenMockAddress, accessCtrlMockAddress, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.SALT.TOKEN_TRANSFER, _deadline])
    return castReturnType(
      jpyTokenTransferBridge.setAddress(ibcTokenMockAddress, accessCtrlMockAddress, _deadline, _sig[0]),
    )
  },
  registerEscrowAccount: async ({ jpyTokenTransferBridge, zoneId, dstChannelID, escrowAccount, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig =
      sig ??
      privateKey.sig(
        privateKeyForSig,
        ['uint16', 'uint256', 'bytes32', 'uint256'],
        [zoneId, dstChannelID, escrowAccount, _deadline],
      )
    return castReturnType(
      jpyTokenTransferBridge.registerEscrowAccount(zoneId, dstChannelID, escrowAccount, _deadline, _sig[0]),
    )
  },
  unregisterEscrowAccount: async ({ jpyTokenTransferBridge, zoneId, dstChannelID, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig =
      sig ?? privateKey.sig(privateKeyForSig, ['uint16', 'uint256', 'uint256'], [zoneId, dstChannelID, _deadline])
    return castReturnType(jpyTokenTransferBridge.unregisterEscrowAccount(zoneId, dstChannelID, _deadline, _sig[0]))
  },
  transfer: async ({ jpyTokenTransferBridge, options = {} }) => {
    const { accountId, fromZoneId, toZoneId, amount, timeoutHeight, traceId } = options
    return castReturnType(
      jpyTokenTransferBridge.transfer(
        accountId ?? BASE.BRIDGE.ACCOUNT_A,
        fromZoneId ?? BASE.ZONE.FIN,
        toZoneId ?? BASE.ZONE.BIZ,
        amount ?? BASE.BRIDGE.AMOUNT,
        timeoutHeight ?? BASE.TIMEOUT_HEIGHT,
        traceId ?? BASE.TRACE_ID,
      ),
    )
  },
  recvPacket: async ({ ibcHandler, jpyTokenTransferBridge, options = {} }) => {
    const { packetData, fromZoneId, toZoneId, amount, transferType } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)

    return castReturnType(
      ibcHandler.recvPacket(await jpyTokenTransferBridge.getAddress(), packet, await ibcHandler.getAddress()),
    )
  },
  acknowledgementPacket: async ({ ibcHandler, jpyTokenTransferBridge, options = {} }) => {
    const { packetData, fromZoneId, toZoneId, amount, ack, transferType } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)

    return castReturnType(
      ibcHandler.acknowledgementPacket(
        await jpyTokenTransferBridge.getAddress(),
        packet,
        ack ?? BASE.IBC.ACK,
        await ibcHandler.getAddress(),
      ),
    )
  },
  timeoutPacket: async ({ ibcHandler, jpyTokenTransferBridge, options = {} }) => {
    const { packetData, fromZoneId, toZoneId, amount, transferType } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)
    return castReturnType(
      ibcHandler.timeoutPacket(await jpyTokenTransferBridge.getAddress(), packet, await ibcHandler.getAddress()),
    )
  },
  recoverPacket: async ({ jpyTokenTransferBridge, options = {} }) => {
    const {
      sig,
      deadline,
      privateKeyForSig = privateKey.key[BASE.EOA.ADMIN],
      packetData,
      fromZoneId,
      toZoneId,
      amount,
      transferType,
    } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.SALT.TOKEN_TRANSFER, _deadline])

    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)

    return castReturnType(
      jpyTokenTransferBridge.recoverPacket(packet, await jpyTokenTransferBridge.getAddress(), _deadline, _sig[0]),
    )
  },
}
