import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlMockInstance, IBCTokenMockInstance, JPYTokenTransferBridgeInstance } from '@test/common/types'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import privateKey from '@/privateKey'

describe('recoverPacket', () => {
  let accounts: SignerWithAddress[]
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  const setupFixture = async () => {
    ;({ accounts, jpyTokenTransferBridge, ibcTokenMock, accessCtrlMock } =
      await contractFixture<JPYTokenTransferBridgeContractType>())
  }

  const setupEscrowAccountAndAdmin = async () => {
    await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
      jpyTokenTransferBridge,
      zoneId: BASE.ZONE.FIN,
      dstChannelID: BASE.ZONE.BIZ,
      escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
    })
    await accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
  }

  describe('正常系', () => {
    describe('EscrowAccountが登録されていて、残高を保持している状態', () => {
      before(async () => {
        await setupFixture()
        await setupEscrowAccountAndAdmin()
      })

      beforeEach(async () => {
        // IssueVoucherを実行し、アカウントの残高を補填する。
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, BASE.TRACE_ID)
        // TransferToEscrowを実行しEscrowAccountの残高を補填する。
        await jpyTokenTransferBridgeFuncs.transfer({ jpyTokenTransferBridge })
      })

      it('(TransferFromEscrow)FinZoneにおいてBizZoneからpacketを受信した場合に、EscrowAccountの残高が減算されていること', async () => {
        const accountBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        // FinZoneにおいてBizZoneからpacketを受信した場合、TransferFromEscrowが実行される。
        // JPYTokenTransferBridgeのonRecvPacket関数はIBCから呼び出すことを制限しているため、ibcHandler経由で呼び出すようにする。
        await jpyTokenTransferBridgeFuncs.recoverPacket({
          jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.BIZ,
            toZoneId: BASE.ZONE.FIN,
          },
        })

        const accountBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        await expect(accountBalance[0]).to.be.equal(
          BigInt(accountBalanceBefore[0]) + BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT),
        )
        await expect(escrowBalance[0]).to.be.equal(BigInt(escrowBalanceBefore[0]) - BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT))
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('ADMIN権限ではない場合、エラーが返却されること', async () => {
        const result = jpyTokenTransferBridgeFuncs.recoverPacket({
          jpyTokenTransferBridge,
          options: {
            privateKeyForSig: privateKey[1],
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = jpyTokenTransferBridgeFuncs.recoverPacket({
          jpyTokenTransferBridge,
          options: {
            sig: ['0x12345678', ''],
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'

        const result = jpyTokenTransferBridgeFuncs.recoverPacket({
          jpyTokenTransferBridge,
          options: {
            sig: [bad_sig, ''],
          },
        })
        await expect(result).to.be.reverted
      })
    })
  })
})
