import { genPacket } from '@test/BalanceSyncBridge/helpers/utils'
import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { FunctionType } from './types'
import privateKey from '@/privateKey'

export const balanceSyncBridgeFuncs: FunctionType = {
  setAddress: async ({ options = {}, ...args }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options

    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.SALT.BALANCE_SYNC, _deadline])

    return castReturnType(
      args.balanceSyncBridge.setAddress(
        args.ibcTokenMockAddress,
        args.accountMockAddress,
        args.accessCtrlMockAddress,
        _deadline,
        _sig[0],
      ),
    )
  },
  syncTransfer: async ({ balanceSyncBridge, options = {} }) => {
    const { fromAccountId, toAccountId, fromZoneId, amount, timeoutHeight, traceId } = options

    return castReturnType(
      balanceSyncBridge.syncTransfer(
        fromAccountId ?? BASE.BRIDGE.ACCOUNT_A,
        toAccountId ?? BASE.BRIDGE.ACCOUNT_B,
        fromZoneId ?? BASE.ZONE.BIZ,
        amount ?? BASE.BRIDGE.AMOUNT,
        timeoutHeight ?? BASE.TIMEOUT_HEIGHT,
        traceId ?? BASE.TRACE_ID,
      ),
    )
  },
  recvPacket: async ({ ibcHandler, balanceSyncBridge, options = {} }) => {
    const { packetData, fromZoneId, toZoneId } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId)

    return castReturnType(
      ibcHandler.recvPacket(await balanceSyncBridge.getAddress(), packet, await ibcHandler.getAddress()),
    )
  },
  acknowledgementPacket: async ({ ibcHandler, balanceSyncBridge, options = {} }) => {
    const { packetData, fromZoneId, toZoneId, ack } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId)

    return castReturnType(
      ibcHandler.acknowledgementPacket(
        await balanceSyncBridge.getAddress(),
        packet,
        ack ?? BASE.IBC.ACK,
        await ibcHandler.getAddress(),
      ),
    )
  },
  timeoutPacket: async ({ ibcHandler, balanceSyncBridge, options = {} }) => {
    const { packetData, fromZoneId, toZoneId } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId)

    return castReturnType(
      ibcHandler.timeoutPacket(await balanceSyncBridge.getAddress(), packet, await ibcHandler.getAddress()),
    )
  },
  recoverPacket: async ({ balanceSyncBridge, options = {} }) => {
    const {
      sig,
      deadline,
      privateKeyForSig = privateKey.key[BASE.EOA.ADMIN],
      packetData,
      fromZoneId,
      toZoneId,
    } = options

    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.SALT.BALANCE_SYNC, _deadline])

    const packet = genPacket(packetData, fromZoneId, toZoneId)

    return castReturnType(
      balanceSyncBridge.recoverPacket(packet, await balanceSyncBridge.getAddress(), _deadline, _sig[0]),
    )
  },
}
