import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IBCTokenInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { IBCTokenContractType } from '@test/IBCToken/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('checkAdminRole()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, ibcToken } = await contractFixture<IBCTokenContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await setupBasicRoles()
    })

    describe('tokenが登録されている状態', () => {
      it('Admin権限がある場合、trueが返されること', async () => {
        const tx = await ibcTokenFuncs.checkAdminRole({ ibcToken, accounts })

        assertEqualForEachField(tx, { has: true, err: '' })
      })

      it('Admin権限でない署名の場合、エラーが返されること', async () => {
        const tx = await ibcTokenFuncs.checkAdminRole({
          ibcToken,
          accounts,
          options: { eoaKey: BASE.EOA.ACCOUNT },
        })

        assertEqualForEachField(tx, { has: false, err: '' })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const badSig = ethers.keccak256(ethers.toUtf8Bytes('badSig'))
        const tx = await ibcTokenFuncs.checkAdminRole({
          ibcToken,
          accounts,
          options: {
            sig: [badSig, badSig],
          },
        })

        assertEqualForEachField(tx, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })
    })
  })
})
