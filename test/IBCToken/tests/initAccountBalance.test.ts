import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { IBCTokenContractType } from '@test/IBCToken/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { expect } from 'chai'
import { before } from 'mocha'

const TERMINATED_ACCOUNT1 = toBytes32('x490')

describe('initAccountBalance()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let ibcToken: IBCTokenInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token, ibcToken, contractManager, businessZoneAccount } =
      await contractFixture<IBCTokenContractType>())
  }

  const setupIbcAddress = async () => {
    const ibcAddress = await accounts[0]
    const ibcAddressString = await ibcAddress.getAddress()
    return { ibcAddress, ibcAddressString }
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccountsAndTokens = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 300,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 500,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  const setupIbcApps = async (ibcAddressString: string) => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
    })
  }

  const setupBusinessZoneAndTransfers = async (ibcAddress: any) => {
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 300,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
        toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 300,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
        toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  // Helper functions for 準正常系
  const setupAccountsAndTokensForSemiNormal = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, TERMINATED_ACCOUNT1]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
    await validatorFuncs.setTerminated({
      validator,
      accounts,
      options: { accountId: TERMINATED_ACCOUNT1 },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 100,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
  }

  const setupIbcAppsForSemiNormal = async (ibcAddressString: string) => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
  }

  const setupBusinessZoneForSemiNormal = async () => {
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: TERMINATED_ACCOUNT1,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: TERMINATED_ACCOUNT1,
      },
    })
  }

  describe('正常系', () => {
    let ibcAddress
    let ibcAddressString

    before(async () => {
      await setupFixture()
      const ibcData = await setupIbcAddress()
      ibcAddress = ibcData.ibcAddress
      ibcAddressString = ibcData.ibcAddressString
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAccountsAndTokens()
        await setupIbcApps(ibcAddressString)
        await setupBusinessZoneAndTransfers(ibcAddress)
      })
      it('呼び出し元がIBCの場合、エラーがスローされないこと', async () => {
        const beforeData = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })
        assertEqualForEachField(beforeData.accountDataAll, { balance: '300' })

        const result = await ibcTokenFuncs.initAccountBalance({
          ibcToken,
          from: accounts[0],
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
        })
        assertEqualForEachField(result, {})

        const afterData = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })
        assertEqualForEachField(afterData.accountDataAll, { balance: '0' })
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddressString

    before(async () => {
      await setupFixture()
      const ibcData = await setupIbcAddress()
      ibcAddressString = ibcData.ibcAddressString
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAccountsAndTokensForSemiNormal()
        await setupIbcAppsForSemiNormal(ibcAddressString)
        await setupBusinessZoneForSemiNormal()
      })

      it('呼び出し元がIBCではない場合、エラーがスローされること', async () => {
        const result = ibcTokenFuncs.initAccountBalance({
          ibcToken,
          from: accounts[1],
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
        })
        await expect(result).to.be.revertedWith(ERR.IBC.NOT_IBC_CONTRACT)
      })
    })
  })
})
