import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { IBCTokenContractType } from '@test/IBCToken/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { expect } from 'chai'
import { before } from 'mocha'

const TERMINATED_ACCOUNT1 = toBytes32('x490')

describe('issueVoucher()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let ibcToken: IBCTokenInstance
  let contractManager: ContractManagerInstance
  let accounts: SignerWithAddress[]
  let ibcAddress

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager } =
      await contractFixture<IBCTokenContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccountAndTokenForNormal = async () => {
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 300,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
  }

  const setupAccountsAndTokenForSemiNormal = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, TERMINATED_ACCOUNT1]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
    await validatorFuncs.setTerminated({
      validator,
      accounts,
      options: { accountId: TERMINATED_ACCOUNT1 },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 100,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
  }

  const setupIbcApp = async () => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: await ibcAddress.getAddress(),
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAccountAndTokenForNormal()
        await setupIbcApp()
      })

      it('balance, totalSupplyが加算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const amount = 100

        const tx = await ibcTokenFuncs.issueVoucher({
          ibcToken,
          from: ibcAddress,
          amount,
          options: { accountId },
        })

        const expectParams = {
          zoneId: BASE.ZONE_ID.ID0,
          validatorId: BASE.VALID.VALID0.ID,
          accountId,
          accountName,
          amount,
          balance: 300 + 100,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(ibcToken, 'IssueVoucher')
          .withArgs(...Object.values(expectParams))
          .to.emit(account, 'AfterBalance')
          .withArgs(
            [[BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.BALANCE_400)]], // fromBalance (account balance after issueVoucher)
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns array with zoneId and 0 balance)
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account, params: [accountId] })
        assertEqualForEachField(fromAccountData.accountData, {
          balance: BASE.TEST_BALANCES.BALANCE_300 + BASE.TEST_BALANCES.AMOUNT_100,
        })
        const totalSupply = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })
        assertEqualForEachField(totalSupply, {
          totalSupply: BASE.TEST_BALANCES.BALANCE_300 + BASE.TEST_BALANCES.AMOUNT_100,
        })
      })

      it('複数ゾーンを持つアカウントへのissueVoucher実行後にAfterBalanceEmittedイベントが正しく発行されること', async () => {
        // セットアップ: BizZone残高を持つ新しいアカウントを作成
        await validatorFuncs.addAccount({ validator, accounts, options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID } })

        // 初期残高をミント
        await tokenFuncs.mint({
          token,
          accounts,
          amount: BASE.TEST_BALANCES.AMOUNT_200,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })

        // BizZoneをセットアップ
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })

        // バウチャーを発行
        const amount = BASE.TEST_BALANCES.BALANCE_150
        const tx = await ibcTokenFuncs.issueVoucher({
          ibcToken,
          from: ibcAddress,
          amount,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })

        await expect(tx)
          .to.emit(ibcToken, 'IssueVoucher')
          .to.emit(account, 'AfterBalance')
          .withArgs(
            [[BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.AMOUNT_200 + BASE.TEST_BALANCES.BALANCE_150)]], // fromBalance (account balance after issueVoucher: 200 + 150)
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns array with zoneId and 0 balance)
            BASE.TRACE_ID,
          )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAccountsAndTokenForSemiNormal()
        await setupIbcApp()
      })

      it('呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること', async () => {
        const result = ibcTokenFuncs.issueVoucher({ ibcToken, from: accounts[1], amount: 100 })
        await expect(result).to.be.revertedWith(ERR.IBC.NOT_IBC_CONTRACT)
      })

      it('解約済みアカウントへのissueVoucherがエラーになること', async () => {
        const result = ibcTokenFuncs.issueVoucher({
          ibcToken,
          from: ibcAddress,
          amount: 100,
          options: { accountId: TERMINATED_ACCOUNT1 },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_TERMINATED)
      })
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })
})
