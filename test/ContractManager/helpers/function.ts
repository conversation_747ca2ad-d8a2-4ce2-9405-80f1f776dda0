import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { FunctionType } from './types'
import privateKey from '@/privateKey'

/**
 * contractManagerのイベントを呼ぶ関数を持つobject
 */
export const contractManagerFuncs: FunctionType = {
  version: ({ contractManager }) => {
    return castReturnType(contractManager.version())
  },
  setIbcApp: async ({ options = {}, ...args }) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig = sig ?? privateKey.sig(signer, ['address', 'uint256'], [args.ibcAddress, _deadline])

    return castReturnType(
      args.contractManager.connect(args.accounts[0]).setIbcApp(args.ibcAddress, args.ibcAppName, _deadline, _sig[0]),
    )
  },
  setContracts: async ({ contractManager, accounts, addresses, options = {} }) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        [
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'uint256',
        ],
        [...addresses, _deadline],
      )

    return contractManager.connect(accounts[9]).setContracts(
      {
        ctrlAddress: addresses[0],
        providerAddress: addresses[1],
        issuerAddress: addresses[2],
        validatorAddress: addresses[3],
        accountAddress: addresses[4],
        financialZoneAccountAddress: addresses[5],
        businessZoneAccountAddress: addresses[6],
        tokenAddress: addresses[7],
        ibcTokenAddress: addresses[8],
        financialCheckAddress: addresses[9],
        transferProxyAddress: addresses[10],
      },
      _deadline,
      _sig[0],
    )
  },
  accessCtrl: ({ contractManager }) => {
    return castReturnType(contractManager.accessCtrl())
  },
  provider: ({ contractManager }) => {
    return castReturnType(contractManager.getFunction('provider'))
  },
  account: ({ contractManager }) => {
    return castReturnType(contractManager.account())
  },
  financialZoneAccount: ({ contractManager }) => {
    return castReturnType(contractManager.financialZoneAccount())
  },
  businessZoneAccount: ({ contractManager }) => {
    return castReturnType(contractManager.businessZoneAccount())
  },
  validator: ({ contractManager }) => {
    return castReturnType(contractManager.validator())
  },
  issuer: ({ contractManager }) => {
    return castReturnType(contractManager.issuer())
  },
  token: ({ contractManager }) => {
    return castReturnType(contractManager.token())
  },
  ibcToken: ({ contractManager }) => {
    return castReturnType(contractManager.ibcToken())
  },
  financialCheck: ({ contractManager }) => {
    return castReturnType(contractManager.financialCheck())
  },
  transferProxy: ({ contractManager }) => {
    return castReturnType(contractManager.transferProxy())
  },
  ibcApp: ({ contractManager, ibcAppName }) => {
    return castReturnType(contractManager.ibcApp(ibcAppName))
  },
}
