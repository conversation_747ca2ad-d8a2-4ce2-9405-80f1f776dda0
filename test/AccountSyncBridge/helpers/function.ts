import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { FunctionType } from './types'
import { genPacket } from './utils'
import privateKey from '@/privateKey'

export const accountSyncBridgeFuncs: FunctionType = {
  setAddress: async ({
    accountSyncBridge,
    providerMockAddress,
    validatorMockAddress,
    accessCtrlMockAddress,
    businessZoneAccountMockAddress,
    ibcTokenMockAddress,
    options = {},
  }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.SALT.ACCOUNT_SYNC, _deadline])
    return castReturnType(
      accountSyncBridge.setAddress(
        providerMockAddress,
        validatorMockAddress,
        accessCtrlMockAddress,
        businessZoneAccountMockAddress,
        ibcTokenMockAddress,
        _deadline,
        _sig[0],
      ),
    )
  },
  syncAccount: async ({ accountSyncBridge, options = {} }) => {
    const {
      validatorId,
      accountId,
      accountName,
      fromZoneId,
      zoneName,
      accountStatus,
      reasonCode,
      approvalAmount,
      traceId,
      timeoutHeight,
    } = options
    // TODO: exchange timeoutHeight and deadline
    return castReturnType(
      accountSyncBridge.syncAccount(
        validatorId ?? BASE.BRIDGE.VALIDATOR_ID,
        accountId ?? BASE.BRIDGE.ACCOUNT_A,
        accountName ?? BASE.BRIDGE.ACCOUNT_A_NAME,
        fromZoneId ?? BASE.ZONE.BIZ,
        zoneName ?? BASE.ZONE.BIZ_NAME,
        accountStatus ?? BASE.STATUS.APPLYING,
        reasonCode ?? BASE.REASON_CODE.REASON_CODE2,
        approvalAmount ?? BASE.BRIDGE.APPROVE_AMOUNT,
        traceId ?? BASE.TRACE_ID,
        timeoutHeight ?? BASE.TIMEOUT_HEIGHT,
      ),
    )
  },
  recvPacket: async ({ ibcHandler, accountSyncBridge, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN], packetData, fromZoneId } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.BRIDGE.VALIDATOR_ID, _deadline])

    const packet = genPacket(packetData, fromZoneId, _deadline, _sig)

    return castReturnType(
      ibcHandler.recvPacket(await accountSyncBridge.getAddress(), packet, await ibcHandler.getAddress()),
    )
  },
  acknowledgementPacket: async ({ ibcHandler, accountSyncBridge, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN], packetData, fromZoneId, ack } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.BRIDGE.VALIDATOR_ID, _deadline])

    const packet = genPacket(packetData, fromZoneId, _deadline, _sig)

    return castReturnType(
      ibcHandler.acknowledgementPacket(
        await accountSyncBridge.getAddress(),
        packet,
        ack ?? BASE.IBC.ACK,
        await ibcHandler.getAddress(),
      ),
    )
  },
  timeoutPacket: async ({ ibcHandler, accountSyncBridge, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN], packetData, fromZoneId } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.BRIDGE.VALIDATOR_ID, _deadline])

    const packet = genPacket(packetData, fromZoneId, _deadline, _sig)

    return castReturnType(
      ibcHandler.timeoutPacket(await accountSyncBridge.getAddress(), packet, await ibcHandler.getAddress()),
    )
  },
  recoverPacket: async ({ accountSyncBridge, options = {} }) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN], packetData, fromZoneId } = options
    const _deadline = await getDeadline(deadline)
    if (!privateKeyForSig) {
      throw new Error('privateKeyForSig is null or undefined')
    }
    const _sig = sig ?? privateKey.sig(privateKeyForSig, ['bytes32', 'uint256'], [BASE.SALT.ACCOUNT_SYNC, _deadline])

    const packet = genPacket(packetData, fromZoneId, _deadline, _sig)

    return castReturnType(
      accountSyncBridge.recoverPacket(packet, await accountSyncBridge.getAddress(), _deadline, _sig[0]),
    )
  },
}
