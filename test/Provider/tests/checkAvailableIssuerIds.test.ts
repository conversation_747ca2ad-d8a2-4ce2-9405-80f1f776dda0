import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, getDeadline } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('checkAvailableIssuerIds()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ProviderContractType>())
  }

  const setupCompleteEnvironment = async () => {
    const pramsAccounts = Object.values(BASE.ACCOUNT)
      .filter((v) => typeof v === 'object')
      .slice(0, 4)

    const params = pramsAccounts.map((v, i) => {
      return {
        accountId: v.ID,
        accountName: v.NAME,
        accountEoa: accounts[i].getAddress(),
        accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
      }
    })

    const deadline = await getDeadline()
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        name: BASE.ISSUER.ISSUER1.NAME,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({
      validator,
      accounts,
      options: {
        validatorEoa: BASE.VALID.EOA_ADDRESS,
      },
    })
    for (const v of params) {
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: v.accountId,
          accountName: v.accountName,
        },
      })
      await issuerFuncs.addAccountRole({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: v.accountId,
          deadline: deadline + 60,
        },
      })
    }
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME2,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        issuerId: BASE.ISSUER.ISSUER0.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })

    return params
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, roleが登録されている状態', () => {
      before(async () => {
        await setupCompleteEnvironment()
      })

      it('認可イシュアリストが取得できること', async () => {
        const result = await providerFuncs.checkAvailableIssuerIds({
          provider,
          options: [BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assertEqualForEachField(result, { success: true, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, roleが登録されている状態', () => {
      before(async () => {
        await setupCompleteEnvironment()
      })

      it('ゾーンに紐付けしていないイシュアに対応するアカウントを指定した場合、エラーが返却されること', async () => {
        const result = await providerFuncs.checkAvailableIssuerIds({
          provider,
          options: [BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT5.ID],
        })

        assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('ゾーンに紐付けしているイシュアが存在しない場合、エラーが返却されること', async () => {
        const result = await providerFuncs.checkAvailableIssuerIds({
          provider,
          options: [BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT4.ID],
        })

        assertEqualForEachField(result, { success: false, err: ERR.ISSUER.ISSUER_ID_NOT_EXIST })
      })
    })
  })
})
