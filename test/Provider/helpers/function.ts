import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { ProviderFunctionType } from './types'
import privateKey from '@/privateKey'

/**
 * providerのイベントを呼ぶ関数を持つobject
 */
export const providerFuncs: ProviderFunctionType = {
  version: ({ provider }) => {
    return castReturnType(provider.version())
  },
  getProvider: ({ provider }) => {
    return castReturnType(provider.getProvider())
  },
  getZone: ({ provider }) => {
    return castReturnType(provider.getZone())
  },
  getZoneName: ({ provider, options }) => {
    return castReturnType(provider.getZoneName(...options))
  },
  getProviderCount: ({ provider }) => {
    return castReturnType(provider.getProviderCount())
  },
  getToken: ({ provider, options }) => {
    return castReturnType(provider.getToken(...options))
  },
  getTokenId: ({ provider }) => {
    return castReturnType(provider.getTokenId())
  },
  getAvailableIssuerIds: ({ provider, options }) => {
    return castReturnType(provider.getAvailableIssuerIds(...options))
  },
  hasToken: ({ provider, options }) => {
    return castReturnType(provider.hasToken(...options))
  },
  hasProvider: ({ provider, options }) => {
    return castReturnType(provider.hasProvider(...options))
  },
  addProvider: async ({ provider, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      providerId = BASE.PROV.PROV0.ID,
      zoneId = BASE.ZONE_ID.ID0,
      zoneName = BASE.ZONE_NAME.NAME0,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint16', 'uint256'], [providerId, zoneId, _deadline])

    return castReturnType(
      provider.connect(accounts[9]).addProvider(providerId, zoneId, zoneName, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  addBizZone: async ({ provider, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      zoneId = BASE.ZONE_ID.ID1,
      zoneName = BASE.ZONE_NAME.NAME1,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['uint16', 'string', 'uint256'], [zoneId, zoneName, _deadline])

    return castReturnType(provider.connect(accounts[9]).addBizZone(zoneId, zoneName, _deadline, _sig[0]))
  },
  addProviderRole: async ({ provider, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, providerId = BASE.PROV.PROV0.ID } = options
    let { providerEoa } = options
    providerEoa = providerEoa ?? (await accounts[BASE.EOA.PROV1].getAddress())
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [providerId, providerEoa, _deadline])

    return castReturnType(
      provider.connect(accounts[9]).addProviderRole(providerId, providerEoa, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  modZone: async ({ provider, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      providerId = BASE.PROV.PROV0.ID,
      zoneName = BASE.ZONE_NAME.NAME0,
      traceId = BASE.TRACE_ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint256'], [providerId, _deadline])

    return castReturnType(provider.connect(accounts[9]).modZone(providerId, zoneName, traceId, _deadline, _sig[0]))
  },
  addToken: async ({ provider, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.PROV1,
      providerId = BASE.PROV.PROV0.ID,
      tokenId = BASE.TOKEN.TOKEN1.ID,
      name = BASE.TOKEN.TOKEN1.NAME,
      symbol = BASE.TOKEN.TOKEN1.SYMBOL,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['uint256', 'uint256', 'bytes32', 'bytes32', 'uint256'],
        [providerId, tokenId, name, symbol, _deadline],
      )

    return castReturnType(
      provider.connect(accounts[9]).addToken(providerId, tokenId, name, symbol, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  modToken: async ({ provider, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.PROV1,
      tokenId = BASE.TOKEN.TOKEN1.ID,
      name = BASE.TOKEN.EMPTY,
      symbol = BASE.TOKEN.EMPTY,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [tokenId, name, symbol, _deadline],
      )

    return castReturnType(
      provider.connect(accounts[9]).modToken(tokenId, name, symbol, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  modProvider: async ({ provider, accounts, providerName, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, providerId = BASE.PROV.PROV0.ID } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [providerId, providerName, _deadline])

    return castReturnType(
      provider.connect(accounts[9]).modProvider(providerId, providerName, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  checkRole: async ({ provider, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.PROV1, providerId = BASE.PROV.PROV0.ID } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [providerId, 'morning', _deadline])

    return castReturnType(provider.checkRole(providerId, _sig[1], _deadline, _sig[0]))
  },
  checkAvailableIssuerIds: async ({ provider, options }) => {
    return castReturnType(provider.checkAvailableIssuerIds(...options))
  },
  getProviderAll: async ({ provider, providerId }) => {
    return castReturnType(provider.getProviderAll(providerId))
  },
  setProviderAll: async ({ provider, prams, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [BASE.SALTS.SET_PROVIDER_ALL, _deadline])
    return castReturnType(provider.setProviderAll(prams, _deadline, _sig[0]))
  },
}
