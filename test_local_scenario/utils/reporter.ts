'use strict'

import Base from 'mocha/lib/reporters/base'
import { constants } from 'mocha/lib/runner'
import { inherits } from 'mocha/lib/utils'
import ms from 'ms'

const {
  EVENT_RUN_BEGIN,
  EVENT_RUN_END,
  EVENT_SUITE_BEGIN,
  EVENT_SUITE_END,
  EVENT_TEST_FAIL,
  EVENT_TEST_PASS,
  EVENT_TEST_PENDING,
} = constants

export = CustomReporter

const getFullErrorStack = function (err, seen = new Set()) {
  if (seen && seen.has(err)) {
    return { message: '', msg: '<circular>', stack: '' }
  }

  let message

  if (typeof err.inspect === 'function') {
    message = err.inspect() + ''
  } else if (err.message && typeof err.message.toString === 'function') {
    message = err.message + ''
  } else {
    message = ''
  }

  let msg
  let stack = err.stack || message
  const index = message ? stack.indexOf(message) : -1

  if (index === -1) {
    msg = message
  } else {
    const adjustedIndex = index + message.length
    msg = stack.slice(0, adjustedIndex)
    stack = stack.slice(adjustedIndex + 1)

    if (err.cause) {
      seen = seen || new Set()
      seen.add(err)
      const causeStack = getFullErrorStack(err.cause, seen)
      stack += '\n   Caused by: ' + causeStack.msg + (causeStack.stack ? '\n' + causeStack.stack : '')
    }
  }

  return {
    message,
    msg,
    stack,
  }
}

function extractLastTestPath(stackTrace) {
  const regex = /tasks\/[^:]+\.ts:\d+:\d+/
  const match = stackTrace.match(regex)
  return match ? match[0] : null
}

function CustomReporter(this: any, runner, options) {
  Base.call(this, runner, options)

  let indents = 0
  let n = 0

  function indent() {
    return Array(indents).join('  ')
  }

  runner.on(EVENT_RUN_BEGIN, () => {
    Base.consoleLog()
  })

  runner.on(EVENT_SUITE_BEGIN, (suite) => {
    ++indents
    Base.consoleLog(Base.color('suite', '%s%s'), indent(), suite.title)
  })

  runner.on(EVENT_SUITE_END, () => {
    --indents
    if (indents === 1) {
      Base.consoleLog()
    }
  })

  runner.on(EVENT_TEST_PENDING, (test) => {
    const fmt = indent() + Base.color('pending', '  - %s')
    Base.consoleLog(fmt, test.title)
  })

  runner.on(EVENT_TEST_PASS, (test) => {
    let fmt
    if (test.speed === 'fast') {
      fmt = indent() + Base.color('checkmark', '  ' + Base.symbols.ok) + Base.color('pass', ' %s')
      Base.consoleLog(fmt, test.title)
    } else {
      fmt =
        indent() +
        Base.color('checkmark', '  ' + Base.symbols.ok) +
        Base.color('pass', ' %s') +
        Base.color(test.speed, ' (%dms)')
      Base.consoleLog(fmt, test.title, test.duration)
    }
  })

  runner.on(EVENT_TEST_FAIL, (test) => {
    Base.consoleLog(indent() + Base.color('fail', '  %d) %s'), ++n, test.title)
  })

  runner.on(EVENT_RUN_END, () => {
    console.log('Error:')
    const stats = this.stats

    if (stats.failures) {
      Base.consoleLog()
      this.failures.forEach((test, i) => {
        const fmt = Base.color('error title', '  %s) %s:\n') + Base.color('error message', '     %s')

        let err
        if (test.err && test.err.multiple) {
          err = test.err.multiple.shift() || test.err
        } else {
          err = test.err
        }

        let { msg } = getFullErrorStack(err)

        if (err.uncaught) {
          msg = 'Uncaught ' + msg
        }

        const testTitle = test
          .titlePath()
          .map((str, index) => '  '.repeat(index) + str)
          .join('\n     ')

        Base.consoleLog(fmt, i + 1, testTitle, msg)
      })
    }

    console.log('Summary:')
    Base.consoleLog()
    const fmt = Base.color('bright pass', ' ') + Base.color('green', ' %d passing') + Base.color('light', ' (%s)')

    Base.consoleLog(fmt, stats.passes || 0, ms(stats.duration))

    if (stats.pending) {
      const pendingFmt = Base.color('pending', ' ') + Base.color('pending', ' %d pending')
      Base.consoleLog(pendingFmt, stats.pending)
    }

    if (stats.failures) {
      const failFmt = Base.color('fail', '  %d failing')
      Base.consoleLog(failFmt, stats.failures)

      this.failures.forEach((test, i) => {
        try {
          const fmt = Base.color('error title', '  %s) %s:\n') + Base.color('error message', '     %s')

          let err
          if (test.err && test.err.multiple) {
            err = test.err.multiple.shift() || test.err
          } else {
            err = test.err
          }

          const { msg: initialMsg, stack } = getFullErrorStack(err)
          let msg = initialMsg

          if (err.uncaught) {
            msg = 'Uncaught ' + msg
          }

          const testPath = extractLastTestPath(stack)
          if (testPath !== null) {
            Base.consoleLog(fmt, i + 1, extractLastTestPath(stack), msg)
          } else {
            Base.consoleLog(fmt, i + 1, test.title, msg)
          }
        } catch (e) {
          console.log(e)
        }
      })
    }
  })
}

inherits(CustomReporter, Base)

CustomReporter.description = 'hierarchical & verbose [default]'
