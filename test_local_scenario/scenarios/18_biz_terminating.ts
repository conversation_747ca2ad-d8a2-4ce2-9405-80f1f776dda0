import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { BizTerminatingTask } from '../tasks/BizTerminatingTask'
import { CheckSyncAccountTask } from '../tasks/CheckSyncAccountTask'
import { TransferTask } from '../tasks/TransferTask'
import { ERROR, ERROR_CODE, extractAccountInfo, showAccountsStatus, SUCCESS } from './utils'

async function processBizTerminating(network: Network, accountId: string) {
  const checkSyncAccountTask = new CheckSyncAccountTask(Network.LocalFin)
  const checkResult = await checkSyncAccountTask.execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
    zoneId: networkConfig[network].ZONE_ID,
    accountStatus: commonConfig.STATUS_TERMINATING,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    const bizTerminatingTask = new BizTerminatingTask(network)
    return await bizTerminatingTask.execute({
      accountId,
    })
  } else {
    console.error('failed checkSyncAccount.')
    throw new Error(`failed checkSyncAccount for account ${accountId}.`)
  }
}

async function checkTerminatingAccountBalanceNotZero(network: Network) {
  const accountId = networkConfig[network].ACCOUNT_ID_3
  const accountStatus = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountStatus.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountStatus.finZone.balance !== '50' ||
    accountStatus.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountStatus.finZone.bizZoneAccountBalance !== '0' ||
    accountStatus.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountStatus.bizZone.balance !== '0'
  ) {
    return ERROR
  }

  console.info('start charge to account3')
  const chargeOutput = await new TransferTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '50',
  })
  if (!chargeOutput.includes(SUCCESS)) {
    return ERROR
  }
  await new Promise((resolve) => setTimeout(resolve, 15000))
  const afterChargeAccountStatus = extractAccountInfo(await showAccountsStatus(accountId))

  if (
    afterChargeAccountStatus.finZone.status !== commonConfig.STATUS_ACTIVE ||
    afterChargeAccountStatus.finZone.balance !== '0' ||
    afterChargeAccountStatus.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    afterChargeAccountStatus.finZone.bizZoneAccountBalance !== '50' ||
    afterChargeAccountStatus.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    afterChargeAccountStatus.bizZone.balance !== '50'
  ) {
    return ERROR
  }

  const checkTerminatingResult = await new CheckSyncAccountTask(Network.LocalFin).execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
    zoneId: networkConfig[Network.LocalBiz].ZONE_ID,
    accountStatus: commonConfig.STATUS_TERMINATING,
  })

  if (!checkTerminatingResult.includes(ERROR_CODE.ACCOUNT_BALANCE_NOT_ZERO)) {
    return ERROR
  }
  console.info('start discharge from account3')
  const dischargeResult = await new TransferTask(Network.LocalBiz).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
    amount: '50',
  })

  if (!dischargeResult.includes(SUCCESS)) {
    return ERROR
  }
  await new Promise((resolve) => setTimeout(resolve, 15000))
  const afterDischargeStatus = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    afterDischargeStatus.finZone.status !== commonConfig.STATUS_ACTIVE ||
    afterDischargeStatus.finZone.balance !== '50' ||
    afterDischargeStatus.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    afterDischargeStatus.finZone.bizZoneAccountBalance !== '0' ||
    afterDischargeStatus.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    afterDischargeStatus.bizZone.balance !== '0'
  ) {
    return ERROR
  }

  return SUCCESS
}

async function bizTerminating(network: Network) {
  const config = networkConfig[network]

  await processBizTerminating(network, config.ACCOUNT_ID_3)
  await processBizTerminating(network, config.ACCOUNT_ID_4)

  await new Promise((resolve) => setTimeout(resolve, 15000))

  const account3Status = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_3))
  if (
    account3Status.finZone.status !== commonConfig.STATUS_ACTIVE ||
    account3Status.finZone.bizZoneAccountStatus !== commonConfig.STATUS_TERMINATING ||
    account3Status.bizZone.status !== commonConfig.STATUS_TERMINATED
  ) {
    return ERROR
  }

  const account4Status = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_4))
  if (
    account4Status.finZone.status !== commonConfig.STATUS_ACTIVE ||
    account4Status.finZone.bizZoneAccountStatus !== commonConfig.STATUS_TERMINATING ||
    account4Status.bizZone.status !== commonConfig.STATUS_TERMINATED
  ) {
    return ERROR
  }
  return SUCCESS
}

export { bizTerminating, checkTerminatingAccountBalanceNotZero }
