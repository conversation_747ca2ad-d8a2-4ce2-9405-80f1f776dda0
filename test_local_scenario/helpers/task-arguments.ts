interface RegisterIssuerArguments {
  issuerId: string
  bankCode: string
  issuerName: string
  issuerKey: string
  flag: string
}

interface RegisterValidArguments {
  validId: string
  issuerId: string
  validName: string
  validKey: string
  flag: string
}

interface GetAccountAllArguments {
  accountId: string
  validId: string
}

interface GetAccountAllListArguments {
  validId: string
  limit: string
  offset: string
  printFullAccountInfo: string
}

interface RegisterProvArguments {
  provId: string
  zoneId: string
  zoneName: string
  provKey: string
  flag: string
}

interface RegisterMassAccountsArguments {
  numberOfAccounts: string
  accountId: string
  accountName: string
  validId: string
}

interface RegisterTokenArguments {
  provKey: string
  tokenId: string
  provId: string
  tokenName: string
  symbol: string
}

interface RegisterAccArguments {
  accountId: string
  accountName: string
  accountKey: string
  issuerKey: string
  issuerId: string
  validId: string
  flag: string
  limitValues: string
}
interface MintTokenArguments {
  issuerId: string
  accountId: string
  amount: string
  issuerKey: string
}

interface BurnTokenArguments {
  issuerId: string
  accountId: string
  amount: string
  issuerKey: string
}

interface ApproveArguments {
  validId: string
  ownerId: string
  spenderId: string
  amount: string
}

interface CheckApproveArguments extends ApproveArguments {
  validKey: string
}

interface CheckFinAccountStatusArguments {
  accountId: string
}

interface MintRenewableArguments {
  tokenId: string
  metadataId: string
  metadataHash: string
  mintAccountId: string
  ownerAccountId: string
  isLocked: string
}

interface RegisterBizZoneArguments {
  zoneId: string
  zoneName: string
}

interface GetTokenArguments {
  tokenId: string
}

interface GetTokenInfoArguments {
  providerId: string
}

interface RenewableArguments {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
}

interface CheckTransactionRenewableArguments extends RenewableArguments {
  misc2: string
}

interface DvpArguments extends RenewableArguments {
  amount: string
  misc2: string
  memo: string
}

interface DvpMultiArguments extends RenewableArguments {
  amount: string
  tokenId1: string
  tokenId2: string
  memo: string
}

interface IndustryAccountArguments {
  validatorId: string
  accountId: string
  zoneId: string
}

interface CheckExchangeArguments {
  accountId: string
  toZoneId: string
  fromZoneId: string
  amount: string
}

interface RegisterEscrowAccArguments {
  srcZoneId: string
  dstZoneId: string
  escrowAccount: string
}

interface TransferArguments {
  accountId: string
  fromZoneId: string
  toZoneId: string
  amount: string
  timeoutHeight: string
}

interface CheckTransactionArguments {
  sendAccountId: string
  fromAccountId: string
  fromAccountIssuerId: string
  toAccountId: string
  zoneId: string
  validId: string
  amount: string
}

interface TransferSingleArguments {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  amount: string
  miscValue1: string
  miscValue2: string
  memo: string
  traceId: string
}

interface GetBizZoneAccountStatusArguments {
  accountId: string
  zoneId: string
}

interface SetAccountStatusArguments {
  issuerId: string
  accountId: string
  accountStatus: string
  reasonCode: string
  issuerKey: string
}

interface ForceBurnTokenArguments {
  issuerId: string
  accountId: string
  issuerKey: string
}

interface PartialForceBurnTokenArguments {
  issuerId: string
  accountId: string
  issuerKey: string
  burnedAmount: string
  burnedBalance: string
}

interface CheckSyncAccountArguments {
  accountId: string
  validId: string
  zoneId: string
  accountStatus: string
}

interface SyncAccountArguments {
  validatorId: string
  accountId: string
  accountName: string
  fromZoneId: string
  zoneName: string
  accountStatus: string
  reasonCode: string
  approvalAmount: string
  traceId: string
  timeoutHeight: string
}

interface SetBizZoneTerminatedArguments {
  accountId: string
  zoneId: string
}

interface DischargeFromFinArguments {
  accountId: string
  fromZoneId: string
  toZoneId: string
  amount: string
  timeoutHeight: string
}

type RetrieveDischargeEventArguments = {}

type RetrieveForceBurnEventArguments = {}

interface AddBizZoneToIssuerArguments {
  issuerId: string
  zoneId: string
}

interface LimitValues {
  mint: number
  burn: number
  charge: number
  discharge: number
  transfer: number
  cumulative: {
    total: number
    mint: number
    burn: number
    charge: number
    discharge: number
    transfer: number
  }
}

interface ModTokenLimitArguments {
  issuerId: string
  accountId: string
  issuerKey: string
  limitUpdates: string
  limitValues: string
}

interface GetIssuerWithZoneArguments {
  zoneId: string
}

interface ModProviderArguments {
  provId: string
  provName: string
}

interface SetTokenEnabledArguments {
  provKey: string
  provId: string
  tokenId: string
  enabled: string
}

interface SetTerminatedArguments {
  validatorId: string
  accountId: string
  reasonCode: string
}

interface CheckBackupFilesArguments {
  path: string
}

interface AddValidatorAccountIdArguments extends BaseTaskArguments {
  validId: string
  accountId: string
  traceId: string
}

export type {
  AddBizZoneToIssuerArguments,
  AddValidatorAccountIdArguments,
  ApproveArguments,
  BurnTokenArguments,
  CheckApproveArguments,
  CheckBackupFilesArguments,
  CheckExchangeArguments,
  CheckFinAccountStatusArguments,
  CheckSyncAccountArguments,
  CheckTransactionArguments,
  CheckTransactionRenewableArguments,
  DischargeFromFinArguments,
  DvpArguments,
  DvpMultiArguments,
  ForceBurnTokenArguments,
  PartialForceBurnTokenArguments,
  GetAccountAllArguments,
  GetAccountAllListArguments,
  GetBizZoneAccountStatusArguments,
  GetIssuerWithZoneArguments,
  GetTokenArguments,
  GetTokenInfoArguments,
  IndustryAccountArguments,
  MintRenewableArguments,
  MintTokenArguments,
  RegisterAccArguments,
  RegisterMassAccountsArguments,
  RegisterBizZoneArguments,
  RegisterEscrowAccArguments,
  RegisterIssuerArguments,
  RegisterProvArguments,
  RegisterTokenArguments,
  RegisterValidArguments,
  RetrieveDischargeEventArguments,
  RetrieveForceBurnEventArguments,
  SetAccountStatusArguments,
  SetBizZoneTerminatedArguments,
  SetTerminatedArguments,
  SetTokenEnabledArguments,
  SyncAccountArguments,
  TransferArguments,
  TransferSingleArguments,
  ModTokenLimitArguments,
  LimitValues,
  ModProviderArguments,
}
