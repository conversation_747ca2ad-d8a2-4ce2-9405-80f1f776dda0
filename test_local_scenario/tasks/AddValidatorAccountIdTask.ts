import { Network } from '../helpers/constants'
import { AddValidatorAccountIdArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class AddValidatorAccountIdTask extends BaseTask<AddValidatorAccountIdArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'addValidatorAccountId')
  }

  protected getDefaultArguments(): AddValidatorAccountIdArguments {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      validId: networkConfig.VALID_ID,
      accountId: networkConfig.ACCOUNT_ID_1,
      traceId: commonConfig.TRACE_ID,
    }
  }
}
