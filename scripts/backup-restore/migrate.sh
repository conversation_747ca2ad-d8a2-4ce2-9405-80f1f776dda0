#!/bin/bash

# Exit on error
set -e

# Read command line arguments
MODEL=$1
NETWORK=$2

# Parse additional options
ORIGIN_PATH=""
shift 2  # Remove the first two arguments (model and network)

while [[ $# -gt 0 ]]; do
    case $1 in
        --origin-path)
            ORIGIN_PATH="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 <model> <network> [options]"
            echo ""
            echo "Arguments:"
            echo "  model     Model to migrate (token, accounts, bizaccounts, finaccounts, issuers, provider, validators, tokensbyaccount, retokens, or 'all')"
            echo "  network   Network name (localBiz, localFin, mainBiz, mainFin, etc.)"
            echo ""
            echo "Options:"
            echo "  --origin-path <path>  Custom path for origin files (default: original files stay in place)"
            echo "  --help                Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 token localBiz"
            echo "  $0 all localBiz"
            echo "  $0 all localBiz --origin-path /tmp/custom-origin"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validate input parameters
if [ -z "$MODEL" ] || [ -z "$NETWORK" ]; then
    echo -e "${RED}Error: Missing required parameters${NC}"
    echo "Usage: $0 <model> <network> [options]"
    echo "Example: $0 token localBiz"
    echo "         $0 all localBiz"
    echo "         $0 all localBiz --origin-path /tmp/custom-origin"
    exit 1
fi

# Validate network exists
NETWORK_PATH="scripts/backup-restore/backupfiles/$NETWORK"
if [ ! -d "$NETWORK_PATH" ]; then
    echo -e "${RED}Error: Network directory '$NETWORK' not found${NC}"
    echo "Available networks:"
    ls scripts/backup-restore/backupfiles/
    exit 1
fi

# Create converted directory if it doesn't exist
CONVERTED_PATH="$NETWORK_PATH/converted"
mkdir -p "$CONVERTED_PATH"

echo -e "${BLUE}Starting migration process...${NC}"
echo "Model: $MODEL"
echo "Network: $NETWORK"
if [ -n "$ORIGIN_PATH" ]; then
    echo "Origin path: $ORIGIN_PATH"
fi

# Build command with optional origin path
CMD="npx ts-node scripts/backup-restore/migration/index.ts \"$MODEL\" \"$NETWORK\""
if [ -n "$ORIGIN_PATH" ]; then
    CMD="$CMD --origin-path \"$ORIGIN_PATH\""
fi

# Run migration script
if eval $CMD; then
    echo -e "${GREEN}Migration completed successfully${NC}"
else
    echo -e "${RED}Migration failed${NC}"
    exit 1
fi
