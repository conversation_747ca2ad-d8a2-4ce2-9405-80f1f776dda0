/**
 * A generic constructor type for creating instances of a class.
 */
export type Constructor<T = {}> = new (...args: any[]) => T

/**
 * Converts an array of raw values into a class instance,
 * resolving nested class instances and arrays recursively.
 *
 * @param ModelClass - The class to convert data into
 * @param data - An array of raw values (e.g., from JSON or CSV)
 * @returns An instance of the class with values assigned by position
 */
export function convertDataToModel<T extends Object>(ModelClass: Constructor<T>, data: any[]): T {
  const instance = new ModelClass()
  const keys = Object.keys(instance)

  if (data.length !== keys.length) {
    throw new Error(`Data length mismatch: Expected ${keys.length} fields, got ${data.length}`)
  }

  keys.forEach((key, i) => {
    const raw = data[i]
    const current = instance[key]
    instance[key] = convertValue(raw, current)
  })

  return instance
}

/**
 * Recursively converts a raw value based on the example structure of a current value.
 */
function convertValue(raw: any, template: any): any {
  // Nested object
  if (isClassInstance(template)) {
    const constructor = template.constructor as Constructor<any>
    return Array.isArray(raw) ? convertDataToModel(constructor, raw) : raw
  }

  // Array of objects or primitives
  if (Array.isArray(template)) {
    if (!Array.isArray(raw)) return raw

    const first = template[0]
    if (isClassInstance(first)) {
      const constructor = first.constructor as Constructor<any>
      return raw.map((item) => (Array.isArray(item) ? convertDataToModel(constructor, item) : item))
    }
    return raw
  }

  return raw
}

/**
 * Recursively sorts object keys to match the order of the class definition.
 *
 * @param ModelClass - A class used to determine property order
 * @param input - An object to sort
 * @returns A new object with keys in the order defined by the class
 */
export function sort<T extends Object>(ModelClass: Constructor<T>, input: T): T {
  const instance = new ModelClass()
  const result = {} as T
  const keys = Object.keys(instance)

  keys.forEach((key) => {
    if (!(key in input)) return

    const raw = input[key]
    const current = instance[key]
    result[key] = sortValue(raw, current)
  })

  return result
}

/**
 * Recursively sorts nested object or array values.
 */
function sortValue(raw: any, template: any): any {
  if (raw === null || raw === undefined) return raw

  // Nested object
  if (isClassInstance(template)) {
    const constructor = template.constructor as Constructor<Record<string, any>>
    return isPlainObject(raw) ? sort(constructor, raw) : raw
  }

  // Array of nested objects
  if (Array.isArray(template)) {
    if (!Array.isArray(raw)) return raw

    const itemTemplate = template[0]
    if (isClassInstance(itemTemplate)) {
      const constructor = itemTemplate.constructor as Constructor<Record<string, any>>
      return raw.map((item) => (isPlainObject(item) ? sort(constructor, item) : item))
    }

    return raw // Primitive array
  }

  return raw // Primitive value
}

/**
 * Determines if a value is a class instance (not a plain object).
 */
function isClassInstance(val: any): val is object {
  return typeof val === 'object' && val !== null && !Array.isArray(val) && val.constructor !== Object
}

/**
 * Determines if a value is a plain JS object.
 */
function isPlainObject(val: any): val is Record<string, any> {
  return typeof val === 'object' && val !== null && !Array.isArray(val)
}
