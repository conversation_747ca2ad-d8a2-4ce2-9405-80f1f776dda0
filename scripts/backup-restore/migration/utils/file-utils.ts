import fs from 'fs/promises'
import path from 'path'
import { logger } from './logger'

/**
 * Read a backup file and parse its JSON content
 */
export async function readBackupFile(filePath: string): Promise<any[] | null> {
  try {
    const content = await fs.readFile(filePath, 'utf-8')
    return JSON.parse(content)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to read file ${filePath}: ${errorMessage}`)
    return null
  }
}

/**
 * Write data to a backup file in JSON format
 */
export async function writeBackupFile(filePath: string, data: any[]): Promise<boolean> {
  try {
    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true })

    // Write file with proper formatting
    await fs.writeFile(filePath, JSON.stringify(data), 'utf-8')
    return true
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to write file ${filePath}: ${errorMessage}`)
    return false
  }
}

/**
 * Create MD5 checksum file for the converted data
 */
export async function createChecksumFile(directory: string): Promise<boolean> {
  try {
    const files = await fs.readdir(directory)
    const md5Data: Record<string, string> = {}

    for (const file of files) {
      // Skip md5.json and non-JSON files
      if (file === 'md5.json' || !file.endsWith('.json')) continue

      const filePath = path.join(directory, file)

      // Check if it's a file (not a directory)
      const stats = await fs.stat(filePath)
      if (!stats.isFile()) continue

      const content = await fs.readFile(filePath, 'utf-8')
      const hash = require('crypto').createHash('md5').update(content).digest('hex')

      md5Data[file] = hash
    }

    await writeBackupFile(path.join(directory, 'md5.json'), [md5Data])
    return true
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to create checksum file: ${errorMessage}`)
    return false
  }
}
