/**
 * Logger utility for the migration process.
 * Implements the logging pattern specified in the basic design document.
 */
export const logger = {
  info: (message: string) => {
    console.log(`[INFO] ${message}`)
  },
  error: (message: string, error?: Error) => {
    console.error(`[ERROR] ${message} ${error}`)
  },
  success: (message: string) => {
    console.log(`[SUCCESS] ${message}`)
  },
  warn: (message: string) => {
    console.warn(`[WARN] ${message}`)
  },
}
