import { convertDataToModel, sort } from '@scripts/backup-restore/migration/utils/convert-utils'

/**
 * Base migration class that defines the standard 4-step flow
 *
 * Flow: Array -> Old Model Object -> New Model Object -> Sorted Object -> Array
 *
 * Each individual migration class should extend this and implement only the transform function
 */
export abstract class BaseMigration<T extends Record<string, any>, U extends Record<string, any>> {
  protected abstract oldModelClass: new () => T
  protected abstract newModelClass: new () => U

  /**
   * Transform function that converts old model to new model
   * This is the only method that needs to be implemented by child classes
   *
   * @param oldData - Old model object
   * @returns New model object
   */
  protected abstract transform(oldData: T): U

  /**
   * Execute the standard migration flow
   *
   * @param inputArray - Input array data from backup files
   * @returns Transformed array data
   */
  public execute(inputArray: any[]): any[] {
    // Step 1: Convert array to old model objects
    const oldModelObjects = convertDataToModel(this.oldModelClass, inputArray)

    // Step 2: Transform old model objects to new model objects
    const newModelObjects: Record<string, U> = {}
    for (const [id, oldData] of Object.entries(oldModelObjects)) {
      newModelObjects[id] = this.transform(oldData)
    }

    // Step 3: Sort new model objects by class definition
    const sortedObjects: Record<string, U> = {}
    for (const [id, newData] of Object.entries(newModelObjects)) {
      sortedObjects[id] = sort(this.newModelClass, newData) as U
    }

    // Step 4: Convert sorted objects back to array format
    const resultArray: any[] = []
    for (const sortedData of Object.values(sortedObjects)) {
      const instance = new this.newModelClass()
      const keys = Object.keys(instance)
      const arrayItem = keys.map((key) => sortedData[key])
      resultArray.push(arrayItem)
    }

    return resultArray
  }
}
