import path from 'path'
import { readBackupFile } from './file-manager'

const BACKUP_DIR = path.join(process.cwd(), 'scripts/backup-restore/backupfiles')

// Type validation rules for each model
const MODEL_TYPE_VALIDATORS: Record<string, (item: any[]) => boolean> = {
  token: (item: any[]) => {
    return (
      typeof item[0] === 'string' && // id
      typeof item[1] === 'string' && // name
      typeof item[2] === 'string' && // symbol
      typeof item[3] === 'number' && // decimals (converted from string)
      typeof item[4] === 'boolean'
    ) // active
  },
  accounts: (item: any[]) => {
    return (
      typeof item[0] === 'string' && // id
      typeof item[1] === 'string' && // name
      typeof item[2] === 'string' && // status
      Array.isArray(item[3]) && // tokens array
      typeof item[4] === 'number' && // balance (converted from string)
      typeof item[5] === 'string' && // parent
      typeof item[6] === 'number' && // createdBlock
      typeof item[7] === 'number' && // validFrom
      typeof item[8] === 'number' && // approvedBlock
      typeof item[9] === 'number' && // validTo
      typeof item[10] === 'string' && // escrowId
      typeof item[11] === 'boolean' && // active
      typeof item[12] === 'string' && // owner (address)
      Array.isArray(item[13])
    ) // children array
  },
  bizaccounts: (item: any[]) => MODEL_TYPE_VALIDATORS.accounts(item),
  finaccounts: (item: any[]) => MODEL_TYPE_VALIDATORS.accounts(item),
  issuers: (item: any[]) => {
    return (
      typeof item[0] === 'string' && // id
      typeof item[1] === 'string' && // name
      typeof item[2] === 'boolean'
    ) // active
  },
  validators: (item: any[]) => {
    return (
      typeof item[0] === 'string' && // id
      typeof item[1] === 'string' && // address
      typeof item[2] === 'boolean'
    ) // active
  },
}

/**
 * Check if number of records in original file and after migration file match
 */
export async function isRecordCountConsistent(model: string): Promise<boolean> {
  const network = process.argv[3] // Get network from command line args
  const originalData = await readBackupFile(path.join(BACKUP_DIR, network, `${model}.json`))
  const convertedData = await readBackupFile(path.join(BACKUP_DIR, network, 'converted', `${model}.json`))

  if (!originalData || !convertedData) return false
  return originalData.length === convertedData.length
}

/**
 * Check if a given data array matches the expected structure for a specific model
 */
export function isDataTypeValid(model: string, data: any[]): boolean {
  if (!Array.isArray(data)) return false

  const validator = MODEL_TYPE_VALIDATORS[model.toLowerCase()]
  if (!validator) return true // If no specific validator, assume valid

  return data.every(validator)
}

/**
 * Validates that all required fields are present in the provided data array
 */
export function isRequiredFieldsValid(model: string, requiredFields: string[], data: any[]): boolean {
  if (!Array.isArray(data)) return false

  // Field indices for each model
  const fieldIndices: Record<string, Record<string, number>> = {
    token: {
      id: 0,
      name: 1,
      symbol: 2,
      decimals: 3,
      active: 4,
    },
    accounts: {
      id: 0,
      name: 1,
      status: 2,
      tokens: 3,
      balance: 4,
      active: 11,
    },
    // bizaccounts and finaccounts use same structure as accounts
    issuers: {
      id: 0,
      name: 1,
      active: 2,
    },
    validators: {
      id: 0,
      address: 1,
      active: 2,
    },
  }

  const modelIndices = fieldIndices[model.toLowerCase()] || {}

  return data.every((item) =>
    requiredFields.every((field) => {
      const index = modelIndices[field]
      return index !== undefined && item[index] !== undefined && item[index] !== null
    }),
  )
}

/**
 * Checks whether the order of records is preserved during data migration
 */
export async function isRecordOrderPreserved(model: string, oldIdIndex: number, newIdIndex: number): Promise<boolean> {
  const network = process.argv[3]
  const originalData = await readBackupFile(path.join(BACKUP_DIR, network, `${model}.json`))
  const convertedData = await readBackupFile(path.join(BACKUP_DIR, network, 'converted', `${model}.json`))

  if (!originalData || !convertedData) return false

  return originalData.every((item, index) => item[oldIdIndex] === convertedData[index][newIdIndex])
}
