import path from 'path'
import { Command } from 'commander'
import { logger } from './logger'

export interface ParsedArguments {
  model: string
  network: string
  originPath: string
}

/**
 * Parse and validate command line arguments
 */
export function parseArguments(): ParsedArguments {
  const program = new Command()

  program
    .argument(
      '<model>',
      'Model to migrate (token, accounts, bizaccounts, finaccounts, issuers, provider, validators, tokensbyaccount, retokens, or "all")',
    )
    .argument('<network>', 'Network name (localBiz, localFin, mainBiz, mainFin, etc.)')
    .option('--origin-path <path>', 'Custom path for origin files (default: original files stay in place)')
    .parse(process.argv)

  const [model, network] = program.args
  const options = program.opts()

  return {
    model: model.toLowerCase(),
    network: network,
    originPath: options.originPath || path.join(__dirname, `../../backupfiles/${network}`),
  }
}

/**
 * Get available models for a network by checking file existence
 */
export function getAvailableModels(originPath?: string): string[] {
  const fs = require('fs')
  const availableModels: string[] = []

  try {
    // Check if network directory exists
    if (!fs.existsSync(originPath)) {
      logger.error(`Network directory not found: ${originPath}`)
      return []
    }

    // Read all files in the network directory
    const files = fs.readdirSync(originPath)

    for (const file of files) {
      // Skip md5.json and non-JSON files
      if (file === 'md5.json' || !file.endsWith('.json')) {
        continue
      }

      // Extract model name from filename (remove .json extension)
      const modelName = file.replace('.json', '')
      availableModels.push(modelName)
    }

    return availableModels
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Error reading backup data directory ${originPath}: ${errorMessage}`)
    return []
  }
}

/**
 * Validate that the specified model exists for the network
 */
export function validateModelExists(model: string, originPath?: string): boolean {
  const availableModels = getAvailableModels(originPath)

  if (availableModels.length === 0) {
    logger.error(`No model files found inside ${originPath}`)
    return false
  }
  if (model === 'all') {
    return true
  }

  if (!availableModels.includes(model)) {
    logger.error(`Model '${model}' not found inside ${originPath}`)
    logger.error(`Available models: ${availableModels.join(', ')}`)
    return false
  }

  return true
}
