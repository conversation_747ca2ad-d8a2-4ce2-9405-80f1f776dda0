# Data Migration Tool

This tool migrates backup data from the old format to the new format while preserving data integrity and structure.

## Usage

```bash
# Migrate a single model
./scripts/backup-restore/migrate.sh <model> <network>

# Migrate all available models for a network
./scripts/backup-restore/migrate.sh all <network>
```

## Examples

```bash
# Migrate only token data
./scripts/backup-restore/migrate.sh token localBiz

# Migrate all available models for a network
./scripts/backup-restore/migrate.sh all localBiz
./scripts/backup-restore/migrate.sh all localFin
```

### Available Models

The migration tool supports the following models:

- `token` - Token data migration
- `accounts` - Account data migration
- `bizaccounts` - Business zone account data migration
- `finaccounts` - Financial zone account data migration
- `issuers` - Issuer data migration
- `provider` - Provider data migration
- `validators` - Validator data migration
- `tokensbyaccount` - Tokens by account data migration
- `retokens` - Renewable energy tokens data migration
- `all` - Migrate all available models for the specified network

**Note**: When using `all`, the tool automatically detects which models have backup files in the specified network directory and only processes those models.

### Network Options

- `localBiz` - Local business network
- `localFin` - Local financial network
- `mainBiz` - Main business network
- `mainFin` - Main financial network

## Examples

```bash
# Migrate only token data
./scripts/backup-restore/migrate.sh token localBiz

# Migrate all account types
./scripts/backup-restore/migrate.sh accounts localFin
./scripts/backup-restore/migrate.sh bizaccounts localFin
./scripts/backup-restore/migrate.sh finaccounts localFin

# Migrate everything for a network
./scripts/backup-restore/migrate.sh all mainBiz
```

## Output Structure

The migration tool creates a `converted/` directory within each network directory containing:

- `token.json` - Converted token data
- `accounts.json` - Converted account data
- `bizaccounts.json` - Converted business zone account data
- `finaccounts.json` - Converted financial zone account data
- `issuers.json` - Converted issuer data
- `provider.json` - Converted provider data
- `validators.json` - Converted validator data
- `tokensbyaccount.json` - Converted tokens by account data (if available)
- `retokens.json` - Converted renewable energy tokens data (if available)
- `md5.json` - Checksum file for validation

**Note**: Models like `tokensbyaccount` and `retokens` are only processed if the corresponding backup files exist in the network directory.

## Features

- **Data Integrity**: Preserves original data format without transformation
- **Validation**: Comprehensive validation of data types and structure
- **Error Handling**: Graceful handling of missing files and empty data
- **Flexible Model Support**: Automatically detects available models per network
- **Checksum Generation**: Creates MD5 checksums for validation
- **Detailed Logging**: Provides comprehensive migration status and error reporting

## Running Migration Directly

You can also run the migration tool directly using Node.js:

```bash
# Install dependencies
npm install

# Run migration
npx ts-node scripts/backup-restore/migration/index.ts <model> <network>
```

## Error Handling

The tool handles various error scenarios:

- **Missing Files**: Skips models that don't have backup files
- **Empty Data**: Handles empty arrays and missing data gracefully
- **Invalid Data**: Provides detailed error messages for data format issues
- **Network Differences**: Adapts to different data structures across networks

## Validation

The migration tool performs several validation checks:

1. **Record Count**: Ensures the number of records is preserved
2. **Data Types**: Validates that data types match expected structure
3. **Required Fields**: Checks that all required fields are present
4. **Record Order**: Verifies that record order is maintained

## Notes

- Individual model failures do not stop the entire process when using `all`
- The tool preserves the original data format without any string decoding or type conversion
- Empty arrays and missing files are handled gracefully
- Each network may have different models available
