import path from 'path'
import { parseArguments, getAvailableModels, validateModelExists } from './utils/cli-utils'
import { readBackupFile, writeBackupFile, createChecksumFile } from './utils/file-utils'
import { logger } from './utils/logger'
import { isRecordCountConsistent, isDataTypeValid, isRecordOrderPreserved } from './utils/verify-utils'
const BACKUP_DIR = path.join(__dirname, '../backupfiles')

async function copyOriginFiles(originPath: string, destinationPath: string): Promise<void> {
  if (originPath === destinationPath) {
    return
  }

  const fs = require('fs')

  try {
    // Create destination directory if it doesn't exist
    if (!fs.existsSync(destinationPath)) {
      fs.mkdirSync(destinationPath, { recursive: true })
    }

    // Read all files from origin path
    const files = fs.readdirSync(originPath)

    for (const file of files) {
      const sourceFile = path.join(originPath, file)
      const destFile = path.join(destinationPath, file)

      // Copy file
      fs.copyFileSync(sourceFile, destFile)
      logger.info(`Copied ${file} to ${destinationPath}`)
    }

    logger.info(`Successfully copied all model files from ${originPath} to ${destinationPath}`)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Error copying files from ${originPath} to ${destinationPath}: ${errorMessage}`)
    throw error
  }
}

async function reorganizeFiles(network: string, models: string[], originPath?: string): Promise<void> {
  const fs = require('fs')
  const networkDir = path.join(BACKUP_DIR, network)
  const convertedDir = path.join(networkDir, 'converted')

  logger.info('Reorganizing files:')

  if (originPath) {
    // Custom origin path specified - move original files to custom location
    const originDir = originPath

    // Create origin directory if it doesn't exist
    if (!fs.existsSync(originDir)) {
      fs.mkdirSync(originDir, { recursive: true })
    }

    logger.info(`  Using custom origin path: ${originPath}`)

    for (const model of models) {
      const originalFile = path.join(networkDir, `${model}.json`)
      const convertedFile = path.join(convertedDir, `${model}.json`)
      const originFile = path.join(originDir, `${model}.json`)

      try {
        // Move original file to custom origin folder
        if (fs.existsSync(originalFile)) {
          fs.copyFileSync(originalFile, originFile)
          logger.info(`  Moved ${model}.json to custom origin path`)
        }

        // Move converted file to original location
        if (fs.existsSync(convertedFile)) {
          fs.copyFileSync(convertedFile, originalFile)
          logger.info(`  Moved converted/${model}.json to original location`)
        }
      } catch (error) {
        logger.error(`Error reorganizing ${model}.json: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
  } else {
    // No custom origin path - keep original files in place, just replace with converted files
    logger.info(`  Using default behavior: keeping original files in place`)

    for (const model of models) {
      const originalFile = path.join(networkDir, `${model}.json`)
      const convertedFile = path.join(convertedDir, `${model}.json`)

      try {
        // Move converted file to original location (replacing original)
        if (fs.existsSync(convertedFile)) {
          fs.copyFileSync(convertedFile, originalFile)
          logger.info(`  Moved converted/${model}.json to original location`)
        }
      } catch (error) {
        logger.error(`Error reorganizing ${model}.json: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
  }

  // Remove the converted directory since files are now in original location
  if (fs.existsSync(convertedDir)) {
    fs.rmSync(convertedDir, { recursive: true, force: true })
    logger.info('  Removed converted/ directory')
  }
}

async function validateTransformation(model: string, network: string): Promise<boolean> {
  const networkDir = path.join(BACKUP_DIR, network)
  const convertedFile = path.join(networkDir, 'converted', `${model}.json`)

  try {
    const convertedData = await readBackupFile(convertedFile)
    if (!convertedData) {
      logger.error(`No converted data found for ${model}`)
      return false
    }

    // Validate data types
    if (!isDataTypeValid(model, convertedData)) {
      logger.error(`Invalid data types for ${model}`)
      return false
    }

    // Validate record count consistency
    if (!(await isRecordCountConsistent(model))) {
      logger.error(`Record count mismatch for ${model}`)
      return false
    }

    // Validate record order preservation
    if (!(await isRecordOrderPreserved(model, 0, 0))) {
      logger.error(`Record order not preserved for ${model}`)
      return false
    }

    logger.info(`Validating ${model}.json - Valid`)
    return true
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Error validating ${model}.json: ${errorMessage}`)
    return false
  }
}

async function convertModel(model: string, network: string, originPath: string): Promise<void> {
  const sourceFile = path.join(originPath, `${model}.json`)
  const convertedDir = `${BACKUP_DIR}/${network}`
  const convertedFile = path.join(convertedDir, `${model}.json`)

  try {
    // Create converted directory if it doesn't exist
    const fs = require('fs')
    if (!fs.existsSync(convertedDir)) {
      fs.mkdirSync(convertedDir, { recursive: true })
    }

    // Read backup data
    const backupData = await readBackupFile(sourceFile)
    if (!backupData) {
      throw new Error(`Failed to read backup file: ${sourceFile}`)
    }

    // Normalize data structure
    const records = Array.isArray(backupData) ? backupData : [backupData]

    logger.info(`Converting ${model}.json (${records.length} items)`)

    // TODO: Transform data based on model type
    const transformedData = records

    // Write converted data
    await writeBackupFile(convertedFile, transformedData)
    logger.success(`Successfully converted ${model}.json -> ${convertedFile} (${transformedData.length} items)`)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Error processing ${model}.json: ${errorMessage}`)
    throw error
  }
}

async function validateModel(model: string, network: string): Promise<void> {
  try {
    const isValid = await validateTransformation(model, network)
    if (!isValid) {
      throw new Error(`Validation failed for ${model}`)
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Error validating ${model}.json: ${errorMessage}`)
    throw error
  }
}

async function main() {
  try {
    // Parse command line arguments
    const { model, network, originPath } = parseArguments()

    const originFiles = `${BACKUP_DIR}/${network}/origin/`

    // Copy files from originPath to originFiles
    logger.info(`Copying files from ${originPath} to ${originFiles}`)
    await copyOriginFiles(originPath, originFiles)

    // Validate model exists for the network
    if (!validateModelExists(model, originFiles)) {
      process.exit(1)
    }

    logger.info(`Starting migration process...`)
    logger.info(`Model: ${model}`)
    logger.info(`Network: ${network}`)
    logger.info(`File to migrate path: ${originFiles}`)

    const startTime = Date.now()
    let processedFiles = 0
    let successfulFiles = 0
    const errors: string[] = []

    if (model === 'all') {
      const availableModels = getAvailableModels(originFiles)
      logger.info(`Starting migration of all available models for ${network}: ${availableModels.join(', ')}`)
      // Convert all models
      logger.info('Start converting:')
      for (const modelName of availableModels) {
        try {
          await convertModel(modelName, network, originFiles)
          successfulFiles++
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error)
          errors.push(`${modelName}: ${errorMessage}`)
          logger.error(`Error processing ${modelName}.json: ${errorMessage}`)
        }
        processedFiles++
      }

      // Validate all models
      logger.info('Start validating:')
      for (const modelName of availableModels) {
        try {
          await validateModel(modelName, network)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error)
          errors.push(`Validation ${modelName}: ${errorMessage}`)
          logger.error(`Error validating ${modelName}.json: ${errorMessage}`)
        }
      }

      // Reorganize files
      await reorganizeFiles(network, availableModels, originPath)

      // Create checksum file
      const networkDir = path.join(BACKUP_DIR, network)
      await createChecksumFile(networkDir)

      const duration = (Date.now() - startTime) / 1000
      logger.info(`Summary:`)
      logger.info(`Successfully converted ${successfulFiles}/${processedFiles} models in ${duration.toFixed(3)}s`)
      logger.info(
        `Successfully validated ${availableModels.length}/${availableModels.length} models in ${duration.toFixed(3)}s`,
      )

      console.log('\nMigration Summary:')
      console.log(`Duration: ${duration.toFixed(2)}s`)
      console.log(`Files Processed: ${processedFiles}`)
      console.log(`Files Succeeded: ${successfulFiles}`)

      if (errors.length > 0) {
        console.log('\nErrors encountered:')
        errors.forEach((error) => {
          console.log(`- ${error}`)
        })
        process.exit(1)
      }
    } else {
      // Single model migration
      try {
        await convertModel(model, network, originFiles)
        await validateModel(model, network)
        await reorganizeFiles(network, [model], originPath)

        // Create checksum file
        const networkDir = path.join(BACKUP_DIR, network)
        await createChecksumFile(networkDir)

        const duration = (Date.now() - startTime) / 1000
        logger.info(`Summary:`)
        logger.info(`Successfully converted 1/1 models in ${duration.toFixed(3)}s`)
        logger.info(`Successfully validated 1/1 models in ${duration.toFixed(3)}s`)

        console.log('\nMigration Summary:')
        console.log(`Duration: ${duration.toFixed(2)}s`)
        console.log(`Files Processed: 1`)
        console.log(`Files Succeeded: 1`)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        logger.error(`Migration failed: ${errorMessage}`)
        process.exit(1)
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Unexpected error: ${errorMessage}`)
    process.exit(1)
  }
}

// Run the migration
if (require.main === module) {
  main().catch((error) => {
    logger.error('Migration failed:', error)
    process.exit(1)
  })
}
