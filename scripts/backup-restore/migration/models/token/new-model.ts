/**
 * New Token model based on TokenAll in Struct.sol
 * Keeping original format without string decoding
 */
export class NewTokenModel {
  tokenId: string // bytes32
  name: string // bytes32
  symbol: string // bytes32
  totalSupply: string // uint256 as string
  enabled: boolean // bool

  constructor(data: Partial<NewTokenModel> = {}) {
    this.tokenId = data.tokenId || ''
    this.name = data.name || ''
    this.symbol = data.symbol || ''
    this.totalSupply = data.totalSupply || ''
    this.enabled = data.enabled || false
  }
}
