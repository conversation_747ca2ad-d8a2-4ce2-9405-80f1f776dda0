/**
 * Old Token model based on TokenAll in Struct.sol
 */
export class OldTokenModel {
  tokenId: string // bytes32
  name: string // bytes32
  symbol: string // bytes32
  totalSupply: string // uint256 as string
  enabled: boolean // bool

  constructor(data: any[]) {
    if (data.length < 5) {
      throw new Error('Invalid token data format')
    }

    this.tokenId = data[0]
    this.name = data[1]
    this.symbol = data[2]
    this.totalSupply = data[3]
    this.enabled = data[4]
  }
}
