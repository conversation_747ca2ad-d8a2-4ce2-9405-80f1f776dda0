/**
 * Old Validator model based on ValidatorAll in Struct.sol
 */
export class OldValidatorModel {
  validatorId: string // bytes32
  name: string // bytes32
  issuerId: string // bytes32
  role: string // bytes32
  validatorAccountId: string // bytes32
  enabled: boolean
  validatorIdExistence: boolean
  issuerIdLinkedFlag: boolean
  validatorEoa: string // address
  validAccountExistence: Array<{
    accountId: string // bytes32
    accountIdExistenceByValidatorId: boolean
  }>

  constructor(data: Partial<OldValidatorModel> = {}) {
    this.validatorId = data.validatorId || ''
    this.name = data.name || ''
    this.issuerId = data.issuerId || ''
    this.role = data.role || ''
    this.validatorAccountId = data.validatorAccountId || ''
    this.enabled = data.enabled || false
    this.validatorIdExistence = data.validatorIdExistence || false
    this.issuerIdLinkedFlag = data.issuerIdLinkedFlag || false
    this.validatorEoa = data.validatorEoa || ''
    this.validAccountExistence = data.validAccountExistence || []
  }
}
