/**
 * Old Financial Zone Account model based on FinancialZoneAccountsAll in Struct.sol
 */
export class OldFinancialZoneAccountModel {
  accountId: string // bytes32
  financialZoneAccountData: {
    mintLimit: string // uint256 as string
    burnLimit: string // uint256 as string
    chargeLimit: string // uint256 as string
    transferLimit: string // uint256 as string
    cumulativeLimit: string // uint256 as string
    cumulativeAmount: string // uint256 as string
    cumulativeDate: string // uint256 as string
  }

  constructor(data: Partial<OldFinancialZoneAccountModel> = {}) {
    this.accountId = data.accountId || ''
    this.financialZoneAccountData = data.financialZoneAccountData || {
      mintLimit: '',
      burnLimit: '',
      chargeLimit: '',
      transferLimit: '',
      cumulativeLimit: '',
      cumulativeAmount: '',
      cumulativeDate: '',
    }
  }
}
