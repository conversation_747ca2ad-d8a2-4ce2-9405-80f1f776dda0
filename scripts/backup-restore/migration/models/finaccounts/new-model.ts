/**
 * New Financial Zone Account model keeping original format
 */
export class NewFinancialZoneAccountModel {
  accountId: string // bytes32
  financialZoneAccountData: {
    mintLimit: string // uint256 as string
    burnLimit: string // uint256 as string
    chargeLimit: string // uint256 as string
    dischargeLimit: string // uint256 as string
    transferLimit: string // uint256 as string
    cumulativeLimit: string // uint256 as string
    cumulativeAmount: string // uint256 as string
    cumulativeDate: string // uint256 as string
    cumulativeTransactionLimits: {
      cumulativeMintLimit: string // uint256 as string
      cumulativeMintAmount: string // uint256 as string
      cumulativeBurnLimit: string // uint256 as string
      cumulativeBurnAmount: string // uint256 as string
      cumulativeChargeLimit: string // uint256 as string
      cumulativeChargeAmount: string // uint256 as string
      cumulativeDischargeLimit: string // uint256 as string
      cumulativeDischargeAmount: string // uint256 as string
      cumulativeTransferLimit: string // uint256 as string
      cumulativeTransferAmount: string // uint256 as string
    }
  }

  constructor(data: Partial<NewFinancialZoneAccountModel> = {}) {
    this.accountId = data.accountId || ''
    this.financialZoneAccountData = data.financialZoneAccountData || {
      mintLimit: '',
      burnLimit: '',
      chargeLimit: '',
      dischargeLimit: '',
      transferLimit: '',
      cumulativeLimit: '',
      cumulativeAmount: '',
      cumulativeDate: '',
      cumulativeTransactionLimits: {
        cumulativeMintLimit: '',
        cumulativeMintAmount: '',
        cumulativeBurnLimit: '',
        cumulativeBurnAmount: '',
        cumulativeChargeLimit: '',
        cumulativeChargeAmount: '',
        cumulativeDischargeLimit: '',
        cumulativeDischargeAmount: '',
        cumulativeTransferLimit: '',
        cumulativeTransferAmount: '',
      },
    }
  }
}
