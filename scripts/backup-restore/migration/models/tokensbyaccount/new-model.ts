/**
 * New model interface for tokens by account data
 * This represents the target data structure after migration
 * Keeping identical to old model format
 */
export class NewTokensByAccountModel {
  accountId: string // bytes32
  tokenIds: string[] // array of bytes32 token IDs

  constructor(data: Partial<NewTokensByAccountModel> = {}) {
    this.accountId = data.accountId || ''
    this.tokenIds = data.tokenIds || []
  }
}
