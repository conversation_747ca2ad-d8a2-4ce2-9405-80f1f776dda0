import { NewIssuerModel } from './new-model'
import { OldIssuerModel } from './old-model'

/**
 * Transform account existence data from old format to new
 */
function transformAccountExistence(oldEntry: any): any {
  return {
    accountId: oldEntry.accountId,
    accountIdExistenceByIssuerId: oldEntry.accountIdExistenceByIssuerId,
  }
}

/**
 * Transform issuer data from old model format to new model format.
 * Keeping original format without string decoding.
 *
 * @param oldModel the input data structured in the OldIssuerModel format
 * @returns a new object converted into the NewIssuerModel format
 */
export function transform(oldModel: OldIssuerModel): NewIssuerModel {
  return {
    issuerId: oldModel.issuerId,
    role: oldModel.role,
    name: oldModel.name,
    bankCode: oldModel.bankCode,
    issuerIdExistence: oldModel.issuerIdExistence,
    issuerEoa: oldModel.issuerEoa,
    issuerAccountExistence: oldModel.issuerAccountExistence.map(transformAccountExistence),
  }
}

/**
 * Transform array data from backup file to new model format
 * This wrapper handles the array format conversion
 */
export function transformIssuer(oldData: any[]): any[] {
  // Handle empty arrays
  if (!Array.isArray(oldData) || oldData.length === 0) {
    return []
  }

  if (oldData.length < 7) {
    throw new Error('Invalid issuer data format')
  }

  // The data is already in the correct format, just return it as-is
  // since we're keeping the original format without any transformation
  return oldData
}
