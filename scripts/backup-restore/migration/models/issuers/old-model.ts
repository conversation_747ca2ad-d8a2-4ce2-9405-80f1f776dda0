/**
 * Old Issuer model based on IssuerAll in Struct.sol
 */
export class OldIssuerModel {
  issuerId: string // bytes32
  role: string // bytes32
  name: string
  bankCode: number // uint16
  issuerIdExistence: boolean
  issuerEoa: string // address
  issuerAccountExistence: Array<{
    accountId: string // bytes32
    accountIdExistenceByIssuerId: boolean
  }>

  constructor(data: Partial<OldIssuerModel> = {}) {
    this.issuerId = data.issuerId || ''
    this.role = data.role || ''
    this.name = data.name || ''
    this.bankCode = data.bankCode || 0
    this.issuerIdExistence = data.issuerIdExistence || false
    this.issuerEoa = data.issuerEoa || ''
    this.issuerAccountExistence = data.issuerAccountExistence || []
  }
}
