/**
 * Old model interface for renewable energy tokens data
 * This represents the original data structure from backup files
 */
export class OldRETokensModel {
  tokenId: string // bytes32
  renewableEnergyTokenData: {
    tokenType: string // uint256 as string
    name: string // bytes32
    symbol: string // bytes32
    issuerId: string // bytes32
    accountId: string // bytes32
    validatorId: string // bytes32
    enabled: boolean // bool
  }

  constructor(data: Partial<OldRETokensModel> = {}) {
    this.tokenId = data.tokenId || ''
    this.renewableEnergyTokenData = data.renewableEnergyTokenData || {
      tokenType: '',
      name: '',
      symbol: '',
      issuerId: '',
      accountId: '',
      validatorId: '',
      enabled: false,
    }
  }
}
