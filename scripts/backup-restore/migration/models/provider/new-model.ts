/**
 * New Provider model keeping original format
 */
export class NewProviderModel {
  providerId: string // bytes32
  providerData: {
    role: string // bytes32
    name: string // bytes32
    zoneId: number // uint16
    enabled: boolean
  }
  providerEoa: string // address
  zoneData: Array<{
    zoneId: number // uint16
    zoneName: string // string
    availableIssuerIds: string[] // bytes32[]
  }>

  constructor(data: Partial<NewProviderModel> = {}) {
    this.providerId = data.providerId || ''
    this.providerData = data.providerData || {
      role: '',
      name: '',
      zoneId: 0,
      enabled: false,
    }
    this.providerEoa = data.providerEoa || ''
    this.zoneData = data.zoneData || []
  }
}
