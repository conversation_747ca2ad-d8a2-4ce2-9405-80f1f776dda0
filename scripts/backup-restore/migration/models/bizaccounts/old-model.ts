/**
 * Old Business Zone Account model based on BizAccountsAll in Struct.sol
 */
export class OldBizAccountsModel {
  accountId: string // bytes32
  bizAccountsByZoneId: Array<{
    zoneId: number // uint16
    businessZoneAccountData: {
      accountName: string
      balance: string // uint256 as string
      accountStatus: string // bytes32
      appliedAt: string // uint256 as string
      registeredAt: string // uint256 as string
      terminatingAt: string // uint256 as string
      terminatedAt: string // uint256 as string
    }
    accountIdExistenceByZoneId: boolean
  }>

  constructor(data: Partial<OldBizAccountsModel> = {}) {
    this.accountId = data.accountId || ''
    this.bizAccountsByZoneId = data.bizAccountsByZoneId || []
  }
}
