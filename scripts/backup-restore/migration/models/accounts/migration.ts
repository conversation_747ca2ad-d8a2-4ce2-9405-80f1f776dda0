import { bytes32ToString, toNumber, toTimestamp, toChe<PERSON>umAddress } from '../../utils/convert-utils'
import { NewAccountModel } from './new-model'
import { OldAccountModel } from './old-model'

/**
 * Transform account approval data from old format to new
 */
function transformApproval(oldApproval: any): any {
  return {
    spanderId: bytes32ToString(oldApproval.spanderId),
    spenderAccountName: oldApproval.spenderAccountName,
    allowanceAmount: toNumber(oldApproval.allowanceAmount),
    approvedAt: toTimestamp(oldApproval.approvedAt),
  }
}

/**
 * Transform account data from old model format to new model format.
 * Following the basic design conversion steps.
 *
 * @param oldModel the input data structured in the OldAccountModel format
 * @returns a new object converted into the NewAccountModel format
 */
export function transform(oldModel: OldAccountModel): NewAccountModel {
  return {
    accountId: bytes32ToString(oldModel.accountId),
    accountName: oldModel.accountName,
    accountStatus: bytes32ToString(oldModel.accountStatus),
    zoneIds: oldModel.zoneIds,
    balance: toNumber(oldModel.balance),
    reasonCode: bytes32ToString(oldModel.reasonCode),
    appliedAt: toTimestamp(oldModel.appliedAt),
    registeredAt: toTimestamp(oldModel.registeredAt),
    terminatingAt: toTimestamp(oldModel.terminatingAt),
    terminatedAt: toTimestamp(oldModel.terminatedAt),
    validatorId: bytes32ToString(oldModel.validatorId),
    accountIdExistence: oldModel.accountIdExistence,
    accountEoa: toChecksumAddress(oldModel.accountEoa),
    accountApprovalAll: oldModel.accountApprovalAll.map(transformApproval),
  }
}

/**
 * Transform array data from backup file to new model format
 * This wrapper handles the array format conversion
 */
export function transformAccount(oldData: any[]): any[] {
  if (!Array.isArray(oldData) || oldData.length < 14) {
    throw new Error('Invalid account data format')
  }

  // Map array to old model structure
  const oldModel: OldAccountModel = {
    accountId: oldData[0],
    accountName: oldData[1],
    accountStatus: oldData[2],
    zoneIds: oldData[3],
    balance: oldData[4],
    reasonCode: oldData[5],
    appliedAt: oldData[6],
    registeredAt: oldData[7],
    terminatingAt: oldData[8],
    terminatedAt: oldData[9],
    validatorId: oldData[10],
    accountIdExistence: oldData[11],
    accountEoa: oldData[12],
    accountApprovalAll: oldData[13] || [],
  }

  // Transform using the standard transform function
  const newModel = transform(oldModel)

  // Return as array format for backup file
  return [
    newModel.accountId,
    newModel.accountName,
    newModel.accountStatus,
    newModel.zoneIds,
    newModel.balance,
    newModel.reasonCode,
    newModel.appliedAt,
    newModel.registeredAt,
    newModel.terminatingAt,
    newModel.terminatedAt,
    newModel.validatorId,
    newModel.accountIdExistence,
    newModel.accountEoa,
    newModel.accountApprovalAll.map((approval) => [
      approval.spanderId,
      approval.spenderAccountName,
      approval.allowanceAmount,
      approval.approvedAt,
    ]),
  ]
}
