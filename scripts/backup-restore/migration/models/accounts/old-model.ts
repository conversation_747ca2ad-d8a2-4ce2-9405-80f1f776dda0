/**
 * Old Account model based on AccountsAll in Struct.sol
 */
export interface OldAccountModel {
  accountId: string // bytes32
  accountName: string // string
  accountStatus: string // bytes32
  zoneIds: number[] // uint16[]
  balance: string // uint256 as string
  reasonCode: string // bytes32
  appliedAt: string // uint256 as string
  registeredAt: string // uint256 as string
  terminatingAt: string // uint256 as string
  terminatedAt: string // uint256 as string
  validatorId: string // bytes32
  accountIdExistence: boolean
  accountEoa: string // address
  accountApprovalAll: Array<{
    spanderId: string // bytes32
    spenderAccountName: string
    allowanceAmount: string // uint256 as string
    approvedAt: string // uint256 as string
  }>
}
