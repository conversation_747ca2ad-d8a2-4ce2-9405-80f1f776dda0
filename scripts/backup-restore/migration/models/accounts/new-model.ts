/**
 * New Account model with numeric type conversions
 */
export interface NewAccountModel {
  accountId: string // decoded string
  accountName: string // unchanged
  accountStatus: string // decoded string
  zoneIds: number[] // unchanged
  balance: number // converted to number
  reasonCode: string // decoded string
  appliedAt: number // converted to number
  registeredAt: number // converted to number
  terminatingAt: number // converted to number
  terminatedAt: number // converted to number
  validatorId: string // decoded string
  accountIdExistence: boolean
  accountEoa: string // checksummed address
  accountApprovalAll: Array<{
    spanderId: string // decoded string
    spenderAccountName: string
    allowanceAmount: number // converted to number
    approvedAt: number // converted to number
  }>
}
