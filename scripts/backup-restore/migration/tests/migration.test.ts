import { expect } from 'chai'
import { transformAccount } from '../models/accounts/migration'
import { transformIssuer } from '../models/issuers/migration'
import { transformToken } from '../models/token/migration'
import { transformValidator } from '../models/validators/migration'

describe('Migration Tests', () => {
  describe('Token Migration', () => {
    it('should correctly transform token data', () => {
      // Sample token data from backup
      const oldData = [
        '0x3330303000000000000000000000000000000000000000000000000000000000',
        '0x746f6b656e000000000000000000000000000000000000000000000000000000',
        '0x73796d626f6c0000000000000000000000000000000000000000000000000000',
        '1300',
        true,
      ]

      const result = transformToken(oldData)

      expect(result).to.be.an('array')
      expect(result).to.have.lengthOf(5)
      expect(result[0]).to.equal('300') // decoded ID
      expect(result[1]).to.equal('token') // decoded name
      expect(result[2]).to.equal('symbol') // decoded symbol
      expect(result[3]).to.equal(1300) // converted to number
      expect(result[4]).to.equal(true)
    })
  })

  describe('Account Migration', () => {
    it('should correctly transform account data', () => {
      // Sample account data from backup
      const oldData = [
        '0x3330300000000000000000000000000000000000000000000000000000000000',
        '0x6163636f756e7431000000000000000000000000000000000000000000000000',
        '0x6163746976650000000000000000000000000000000000000000000000000000',
        [],
        '800',
        '0x0000000000000000000000000000000000000000000000000000000000000000',
        '0',
        '**********',
        '0',
        '0',
        '0x3838383800000000000000000000000000000000000000000000000000000000',
        true,
        '0x0000000000000000000000000000000000000000',
        [],
      ]

      const result = transformAccount(oldData)

      expect(result).to.be.an('array')
      expect(result).to.have.lengthOf(14)
      expect(result[0]).to.equal('300') // decoded ID
      expect(result[1]).to.equal('account1') // decoded name
      expect(result[2]).to.equal('active') // decoded status
      expect(result[3]).to.be.an('array') // tokens array
      expect(result[4]).to.equal(800) // converted balance
      expect(result[11]).to.equal(true) // active status
      expect(result[13]).to.be.an('array') // children array
    })

    it('should handle account data with children', () => {
      const oldData = [
        '0x3330300000000000000000000000000000000000000000000000000000000000',
        '0x6163636f756e7431000000000000000000000000000000000000000000000000',
        '0x6163746976650000000000000000000000000000000000000000000000000000',
        [],
        '800',
        '0x0000000000000000000000000000000000000000000000000000000000000000',
        '0',
        '**********',
        '0',
        '0',
        '0x3838383800000000000000000000000000000000000000000000000000000000',
        true,
        '0x0000000000000000000000000000000000000000',
        [
          [
            '0x3330320000000000000000000000000000000000000000000000000000000000',
            '0x6163636f756e7433000000000000000000000000000000000000000000000000',
            '999999',
            '**********',
          ],
        ],
      ]

      const result = transformAccount(oldData)
      const children = result[13]

      expect(children).to.be.an('array')
      expect(children).to.have.lengthOf(1)
      expect(children[0].id).to.equal('302')
      expect(children[0].name).to.equal('account3')
      expect(children[0].balance).to.equal(999999)
      expect(children[0].validFrom).to.equal(**********)
    })
  })

  describe('Validator Migration', () => {
    it('should correctly transform validator data', () => {
      // Sample validator data from backup
      const oldData = [
        '0x3838383800000000000000000000000000000000000000000000000000000000',
        '0x76616c696461746f720000000000000000000000000000000000000000000000',
        '0x3232323200000000000000000000000000000000000000000000000000000000',
        '0xb9b9f1209ec1af837f3362ea0875215ddd0438a51f287c54320e97a526ff7717',
        '0x0000000000000000000000000000000000000000000000000000000000000000',
        true,
        true,
        true,
        '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
        [['0x3330300000000000000000000000000000000000000000000000000000000000', true]],
      ]

      const result = transformValidator(oldData)

      expect(result).to.be.an('array')
      expect(result).to.have.lengthOf(10)
      expect(result[0]).to.equal('8888') // decoded ID
      expect(result[1]).to.equal('validator') // decoded name
      expect(result[2]).to.equal('2222') // decoded issuerId
      expect(result[5]).to.equal(true) // enabled status
      expect(result[8]).to.equal('0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC') // checksummed address
      expect(result[9]).to.be.an('array') // valid accounts array
      expect(result[9][0][0]).to.equal('300') // decoded account ID
    })
  })

  describe('Issuer Migration', () => {
    it('should correctly transform issuer data', () => {
      // Sample issuer data
      const oldData = [
        '0x3232323200000000000000000000000000000000000000000000000000000000',
        '0x697373756572000000000000000000000000000000000000000000000000000000',
        'Test Issuer',
        1234,
        true,
        '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
        [['0x3330300000000000000000000000000000000000000000000000000000000000', true]],
      ]

      const result = transformIssuer(oldData)

      expect(result).to.be.an('array')
      expect(result).to.have.lengthOf(7)
      expect(result[0]).to.equal('2222') // decoded ID
      expect(result[1]).to.equal('issuer') // decoded role
      expect(result[2]).to.equal('Test Issuer') // name unchanged
      expect(result[3]).to.equal(1234) // bankCode unchanged
      expect(result[5]).to.equal('0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC') // checksummed address
      expect(result[6]).to.be.an('array') // account existence array
      expect(result[6][0][0]).to.equal('300') // decoded account ID
    })
  })
})
