import { expect } from 'chai'
import { transformProvider } from '../models/provider/migration'

describe('Provider Migration Tests', () => {
  describe('Model Evolution', () => {
    it('should handle new fields in provider data', () => {
      const oldData = [
        '0x3838383800000000000000000000000000000000000000000000000000000000',
        {
          role: '0x70726f766964657200000000000000000000000000000000000000000000000000',
          name: '0x7465737400000000000000000000000000000000000000000000000000000000',
          zoneId: 1,
          enabled: true,
        },
        '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
        [],
      ]

      const result = transformProvider(oldData)

      // Check original field conversions
      expect(result[0]).to.equal('8888') // decoded providerId
      expect(result[1].role).to.equal('provider') // decoded role
      expect(result[1].name).to.equal('test') // decoded name
      expect(result[1].zoneId).to.equal(1)
      expect(result[1].enabled).to.be.true

      // Check new fields
      expect(result[1]).to.have.property('createdAt').that.is.a('number')
      expect(result[1]).to.have.property('updatedAt').that.is.a('number')
      expect(result[1]).to.have.property('version').that.equals(1)
    })

    it('should add metrics to zone data', () => {
      const oldData = [
        '0x3838383800000000000000000000000000000000000000000000000000000000',
        {
          role: '0x70726f766964657200000000000000000000000000000000000000000000000000',
          name: '0x7465737400000000000000000000000000000000000000000000000000000000',
          zoneId: 1,
          enabled: true,
        },
        '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
        [
          {
            zoneId: 1,
            zoneName: 'Test Zone',
            availableIssuerIds: ['0x3232323200000000000000000000000000000000000000000000000000000000'],
          },
        ],
      ]

      const result = transformProvider(oldData)
      const zoneData = result[3][0]

      // Check original zone data
      expect(zoneData.zoneId).to.equal(1)
      expect(zoneData.zoneName).to.equal('Test Zone')
      expect(zoneData.availableIssuerIds[0]).to.equal('2222') // decoded issuer ID

      // Check new zone fields
      expect(zoneData).to.have.property('status').that.equals('active')
      expect(zoneData).to.have.property('lastSync').that.is.a('number')
      expect(zoneData).to.have.property('metrics').that.deep.includes({
        totalAccounts: 0,
        activeAccounts: 0,
      })
      expect(zoneData.metrics).to.have.property('lastUpdated').that.is.a('number')
    })
  })
})
