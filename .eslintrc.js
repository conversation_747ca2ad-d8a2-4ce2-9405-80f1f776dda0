module.exports = {
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended"],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 13,
    sourceType: "module",
    project: "./tsconfig.json",
    tsconfigRootDir: __dirname,
  },
  plugins: ["@typescript-eslint", "import", "unused-imports"],
  rules: {
    "@typescript-eslint/no-floating-promises": ["error"],
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
      },
    ],
    "@typescript-eslint/no-extra-semi": "off",
    "@typescript-eslint/no-this-alias": ["warn"],
    "@typescript-eslint/no-empty-function": ["warn"],
    "@typescript-eslint/no-empty-interface": ["warn"],
    "prefer-const": ["warn"],
    "no-useless-escape": ["warn"],
    "unused-imports/no-unused-imports": ["warn"],
    "no-control-regex": ["warn"],
    // import の並び替え
    "import/order": [
      "warn",
      {
        groups: [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index",
        ],
        "newlines-between": "never",
        alphabetize: { order: "asc", caseInsensitive: true },
      },
    ],
    // 未使用の変数にも対応（先頭に _ がついてるものは OK）
    "unused-imports/no-unused-vars": [
      "warn",
      { vars: "all", varsIgnorePattern: "^_", argsIgnorePattern: "^_" },
    ],
    // {} を型を 警告 (warn) に変更
    "@typescript-eslint/ban-types": [
      "warn",
      {
        types: {
          "{}": false,
        },
        extendDefaults: true,
      },
    ],
  },
};
