import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Contract } from 'ethers'
import { AccessCtrl } from '@/types/contracts/AccessCtrl'
import { Account } from '@/types/contracts/Account'
import { ContractManager } from '@/types/contracts/ContractManager'
import { Issuer } from '@/types/contracts/Issuer'
import { Provider } from '@/types/contracts/Provider'
import { Token } from '@/types/contracts/Token'
import { TransferProxy } from '@/types/contracts/TransferProxy'
import { Validator } from '@/types/contracts/Validator'

type ContractType = {
  AccessCtrl: AccessCtrl
  Account: Account
  ContractManager: ContractManager
  Error: Contract
  Issuer: Issuer
  Provider: Provider
  Token: Token
  TransferProxy: TransferProxy
  Validator: Validator
}

const TARGET: (keyof ContractType)[] = [
  'AccessCtrl',
  'Account',
  'ContractManager',
  'Error',
  'Issuer',
  'Provider',
  'Token',
  'TransferProxy',
  'Validator',
] as const

wrappedTask('deployConfirmation_main', 'Prints contracts setted to ContractManager', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { deployed, contract } = await getContractWithSigner<ContractType[keyof ContractType]>({
      hre,
      contractName: TARGET[lp],
    })

    // Errorはversion()が無いため、アドレスのみ表示
    if (TARGET[lp] == 'Error') {
      console.log(`${TARGET[lp]}: ${deployed.address}`)
      continue
    }
    const version = await contract.version()
    console.log(`${TARGET[lp]}: "${version}" (${deployed.address})`)
  }
})
